import datetime
import subprocess
import threading
import traceback

import time
import serial

from common.LogUtils import logger
from fs_manager.FSManager import fs_manager
from utils.Influxdb import influx_client

from utils.SignalsManager import signals_manager
from adb.AdbConnectDevice import adb_connect_device


class LogReader:

    def __init__(self, ):
        super(Log<PERSON>eader, self).__init__()
        self.serial = None
        self.error_marks = {}  # 错误标记字典{mark: {"max_count": n, "current_count": 0}}
        self._is_open = False
        self.error_count = {}  # 记录每个错误标记出现的次数
        self.error_limits = {}  # 每个错误标记的最大允许次数
        self.delay_stop_time = 0  # 默认延迟停止时间(秒)
        self.stop_event = threading.Event()
        self.is_running = False
        self.start_stop_lock = threading.Lock()
        self.process_monitor_alert_time = 0

    def is_open(self):
        return self._is_open

    def append_error_mark(self, mark, max_count=1, delay_time=60):
        if mark in self.error_marks: #mark已存在，保持current_count不变，更新max_count和delay_time
            current_count = self.error_marks[mark]["current_count"]
            self.error_marks[mark] = {
                "max_count": max_count,
                "current_count": current_count,
                "delay_time": delay_time
            }
        else:
            self.error_marks[mark] = {
                "max_count": max_count,
                "current_count": 0,
                "delay_time": delay_time
            }

    def clear_error_mark(self):
        self.error_marks.clear()
    
    def check_error_and_emit(self, data, mark):
        if mark in data:
            mark_info = self.error_marks[mark]
            mark_info["current_count"] += 1
            logger.info(f"检测到错误 {mark}, 当前次数: {mark_info['current_count']}, 最大次数: {mark_info['max_count']}")
            
            if mark_info["current_count"] >= mark_info["max_count"]:
                self.delay_stop_time = mark_info["delay_time"]
                signals_manager.detect_log_error.emit(data)
                return True
        return False

    def open(self, port, bit=115200):
        logger.info(f"open port={port}, bit={bit}")
        self.stop_event.set()
        try:
            if port and bit:
                self.serial = serial.Serial(port, bit, timeout=0.1)
                if self.serial.is_open:
                    self._is_open = True
                    return self._is_open
        except Exception as e:
            logger.error("open exception: {}".format(str(e.args)))
        return False

    def close(self):
        """串口关闭"""
        self.stop()
        if self.serial and self.serial.is_open:
            try:
                self.serial.close()
                logger.info("Serial port closed successfully")
            except Exception as e:
                logger.error(f"Error closing serial port: {e}")
        self._is_open = False
        return True

    def stop(self):
        """停止线程并等待其终止"""
        if self.is_running:
            with self.start_stop_lock:
                self.stop_event.set()
            # 给线程一点时间来响应停止事件
            timeout = 1.0  # 最多等待1秒
            start_time = datetime.datetime.now()
            while self.is_running:
                # 等待线程终止，但不要无限等待
                elapsed = (datetime.datetime.now() - start_time).total_seconds()
                if elapsed > timeout:
                    logger.warning(f"Thread did not stop within {timeout} seconds")
                    break
                time.sleep(0.1)  # 短暂休眠避免CPU占用过高        
        # 重置状态
        self.delay_stop_time = 0

    def serial_dtc_detact(self,data):
        try:
            if "$HWSKV," in data:
                kv_data_list = data.split("$HWSKV,")[-1].split(",")
                try:
                    from utils.ProjectManager import project_manager
                    from case.StepManager import step_manager
                    step = step_manager.get_current_step()
                    case_number = step.get("case_number", "")
                    case_name = step.get("case_name", "")
                    project_number = project_manager.get_test_plan_project_number()
                    project_name = project_manager.get_test_plan_project_name()
                    test_plan_name = project_manager.get_test_plan_name()
                    test_plan_id = project_manager.get_test_plan_id()
                    machine_number = project_manager.get_machine_number()
                except:
                    logger.info(traceback.format_exc())

                try:
                    for item in kv_data_list:
                        code = item.split("=")[0]
                        value = item.split("=")[1]
                        influx_client.write_data_multi(
                            table=str(project_number),
                            tags={
                                "project_name": project_name,
                                "test_plan_name": test_plan_name,
                                "test_plan_id": test_plan_id,
                                "machine_number": machine_number,
                                "case_number": case_number,
                                "case_name": case_name,
                                # "channel": f"chl{i + 1}"
                            },
                            fields={code: float(value)})
                        time.sleep(.01)
                except Exception as e:
                    logger.warning(f"serial_dtc_detact error: {traceback.format_exc()}")  # 记录错误日志，以便调试和排除问题

            elif "$HWDTC," in data:
                dtc_data_list =  data.split("$HWDTC,")[-1].split(",")
                try:
                    from utils.ProjectManager import project_manager
                    from case.StepManager import step_manager
                    step = step_manager.get_current_step()
                    uuid = project_manager.get_test_record_id()
                    project_number = project_manager.get_test_plan_project_number()
                    project_name = project_manager.get_test_plan_project_name()
                    test_plan_name = project_manager.get_test_plan_name()
                    test_plan_id = project_manager.get_test_plan_id()
                    # machine_number = project_manager.get_machine_number()
                except:
                    logger.info(traceback.format_exc())

                for dtc_data in dtc_data_list:
                    name = dtc_data.split("=")[0]
                    value = dtc_data.split("=")[1]


                    tmp = {
                        "psn": "null",
                        "extra_info": "",
                        "project_number": project_number,
                        "run_id": uuid,
                        "project_name": project_name
                    }
                    tmp["code"] = name
                    tmp["status"] = value
                    tmp["test_plan_name"] = test_plan_name
                    tmp["test_plan_id"] = test_plan_id
                    tmp["extra_info"] = ""
                    cur_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                    tmp["cur_time"] = cur_time
                    tmp['project_number'] = project_number
                    if int(value) == 1:
                        threading.Thread(target=fs_manager.post_process_monitor_exp_submit, args=(tmp,)).start()
                        if time.time() - self.process_monitor_alert_time > 60:
                            self.process_monitor_alert_time = time.time()
                            data = {
                                "code": tmp["code"],
                                "name": tmp['name'],
                                "cur_time": tmp['cur_time'],
                                "project_number": tmp['project_number'],
                                "test_plan_name": test_plan_name,
                                # "test_plan_id": test_plan_id,
                                "app_name": "ATEApp",
                                "station_name": project_manager.get_machine_number(),
                            }
                            threading.Thread(target=fs_manager.post_process_monitor_send_exp_msg, args=(data,)).start()
        except Exception:
            logger.info(traceback.format_exc())
    def read_serial_msg(self, msg_type='mcu'):
        # 使用线程名称，便于调试
        thread_name = f"{msg_type}_reader_thread"
        threading.current_thread().name = thread_name
        logger.info(f"Starting {thread_name}, serial open={self._is_open}")
        from case.CaseManager import case_manager
        if self.serial is None:
            logger.warning(f"serial is None, {thread_name} exit")
            return
        
        self.serial.timeout = 0.1 #设置超时
        self.is_running = True
        while self.serial.is_open and not self.stop_event.is_set():
            raw_data = None
            data = None
            if self.serial.inWaiting():
                try:
                    raw_data = self.serial.readline()
                    if len(raw_data) > 0:
                        # 尝试多种编码方式
                        try:
                            # 首先尝试UTF-8
                            data = raw_data.decode("utf-8").strip()
                        except UnicodeDecodeError:
                            try:
                                # 然后尝试GBK（常用于中文系统）
                                data = raw_data.decode("gbk").strip()
                            except UnicodeDecodeError:
                                try:
                                    # 再尝试Latin-1（可以解码任何字节）
                                    data = raw_data.decode("latin-1").strip()
                                except UnicodeDecodeError:
                                    # 最后使用replace模式
                                    data = raw_data.decode("utf-8", errors='replace').strip()
                    
                    if not data:
                        continue
                    self.serial_dtc_detact(data)
                    # 检查数据是否包含替换字符，如果包含则记录原始十六进制
                    if '�' in data or '?' in data:
                        hex_data = ' '.join([f'{b:02X}' for b in raw_data])
                        logger.info(f"read_{msg_type}_msg, hex data: {hex_data}")
                    else:
                        logger.info(f"read_{msg_type}_msg data={data}")
                    log_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    
                    # 根据消息类型选择不同的添加方法
                    if msg_type == 'mcu':
                        adb_connect_device.add_mcu_msgs(f"{log_time} {data}")
                        adb_connect_device.add_mcu_msgs("\n")
                    else:
                        adb_connect_device.add_soc_msgs(f"{log_time} {data}")
                        adb_connect_device.add_soc_msgs("\n")
                            
                        if case_manager.abnormal_stop:
                            for mark in self.error_marks:
                                if self.check_error_and_emit(data, mark):
                                    break

                except Exception as e:
                    logger.error(f"read_{msg_type}_msg exception: {str(e.args)}")
                    logger.error(f"Failed to decode raw bytes: {raw_data.hex()}")
        self.is_running = False
        
    # 兼容原方法
    def read_mcu_msg(self):
        self.read_serial_msg('mcu')
        
    def read_soc_msg(self):
        self.read_serial_msg('soc')

    def write_mcu_msg(self, data):
        if self.serial is not None:
            self.serial.write(data)

    def is_device_available(self):
        # 简单的设备检查
        try:
            # `adb devices`的输出通常第一行是 "List of devices attached"
            # 后面是设备列表，例如 "emulator-5554\tdevice"
            # 如果没有设备或adb服务没启动，可能只有第一行或报错
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=5, check=False)
            if result.returncode != 0:
                logger.warning(f"adb devices command failed with code {result.returncode}: {result.stderr}")
                return False
            
            lines = result.stdout.strip().split('\n')
            # 至少应该有 "List of devices attached" 和一个设备行
            for line in lines[1:]: # 跳过 "List of devices attached"
                if "\tdevice" in line and "emulator" not in line.lower() and "unauthorized" not in line.lower() and "offline" not in line.lower(): # 简单过滤模拟器和非正常状态
                    # 安全地分割和访问第一个元素
                    parts = line.split()
                    if len(parts) > 0:
                        logger.info(f"Device found: {parts[0]}")
                        return True
            logger.warning("No suitable physical device found via 'adb devices'.")
            return False # 如果没有找到符合条件的设备
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            logger.error(f"Error checking device availability: {e}")
            return False
        except Exception as e: # 捕获其他潜在的异常
            logger.error(f"Unexpected error during device check: {e}")
            return False

    def cleanup_processes(self):
        logger.info("开始清理Logcat相关进程...")
        for proc in reversed(self.process_list):
            if proc and proc.poll() is None:
                logger.info(f"尝试终止进程 PID: {proc.pid} (命令: {' '.join(proc.args if proc.args else ['N/A'])})")
                proc.terminate()
                try:
                    proc.wait(timeout=1.0)
                    logger.info(f"进程 PID: {proc.pid} 已终止")
                except subprocess.TimeoutExpired:
                    logger.warning(f"终止进程 PID: {proc.pid} 超时，尝试强制结束...")
                    proc.kill()
                    try:
                        proc.wait(timeout=1.0)
                        logger.info(f"进程 PID: {proc.pid} 已强制结束")
                    except subprocess.TimeoutExpired:
                        logger.error(f"强制结束进程 PID: {proc.pid} 后等待超时")
                    except Exception as e_kill_wait:
                        logger.error(f"强制结束后等待进程 PID {proc.pid} 时出错: {e_kill_wait}")
                except Exception as e_term_wait:
                     logger.error(f"终止后等待进程 PID {proc.pid} 时出错: {e_term_wait}")
            
            try: # 确保管道被关闭
                if proc and proc.stdout and not proc.stdout.closed:
                    proc.stdout.close()
                if proc and proc.stderr and not proc.stderr.closed:
                    proc.stderr.close()
            except Exception as e_pipe:
                logger.debug(f"关闭进程 {proc.pid if proc else 'N/A'} 管道时出错: {e_pipe}")
        self.process_list = []
        logger.info("Logcat相关进程清理完毕。")

    def _threaded_log_reader(self, pipe, log_queue, stop_event_local):
        """
        在单独线程中读取管道输出的函数。
        pipe: 要读取的管道 (p_obj.stdout)
        log_queue: 用于将读取的行发送回主线程的队列
        stop_event_local: 用于通知此线程停止的事件
        """
        logger.info("Logcat读取线程已启动。")
        try:
            while not stop_event_local.is_set():
                try:
                    # readline() 是阻塞的，但如果管道关闭或发生错误，它会返回或抛出异常
                    # 设置一个小的超时，以便能周期性检查stop_event (如果subprocess.Popen的stdout支持超时)
                    # 对于subprocess.PIPE，readline()本身不带超时参数。
                    # 它会阻塞直到有新行或EOF。
                    line_bytes = pipe.readline()
                    if line_bytes == b'': # EOF
                        logger.info("Logcat读取线程：检测到EOF，管道已关闭。")
                        log_queue.put(None) # 发送None作为结束信号
                        break
                    log_queue.put(line_bytes)
                except BrokenPipeError: # Python 3.3+
                    logger.warning("Logcat读取线程：管道已损坏 (BrokenPipeError)。")
                    log_queue.put(None)
                    break
                except IOError as e: # 其他IO错误
                    # 在Windows上，当父进程关闭子进程的管道句柄时，子进程中的读取操作
                    # (如readline)可能会失败并引发IOError，例如 "[Errno 22] Invalid argument"
                    # 或者在尝试关闭一个已经被另一端关闭的管道时。
                    if not stop_event_local.is_set(): # 如果不是因为我们主动停止
                        logger.error(f"Logcat读取线程IO异常: {e}")
                    log_queue.put(None)
                    break
                except Exception as e:
                    if not stop_event_local.is_set():
                         logger.error(f"Logcat读取线程发生未知异常: {e}", exc_info=True)
                    log_queue.put(None) # 发送None作为结束信号
                    break
        finally:
            if not pipe.closed:
                try:
                    pipe.close()
                except Exception as e:
                    logger.debug(f"Logcat读取线程关闭管道时出错: {e}")
            logger.info("Logcat读取线程已停止。")

    def start_logcat(self, grep="VideoSourceApp"):
        import queue
        with self.start_stop_lock:
            if self.is_running:
                logger.warning("Logcat已在运行，跳过本次请求")
                return
                
            logger.info(f"准备启动logcat，目标grep: '{grep}'")
            self.is_running = True
            self.stop_event.clear()

        self.process_list = []
        self._log_queue = queue.Queue() # 为这次运行创建新队列
        
        # 检查设备连接状态 (更早的检查)
        if not self.is_device_available():
            logger.warning("未检测到设备，logcat不会启动")
            self.is_running = False
            self._log_queue = queue.Queue()   # 清理队列
            return
        
        p_obj = None
        initialization_successful = True
        try:
            init_commands = [
                ["adb", "wait-for-device"],
                ["adb", "root"], # 'adb root'可能失败在非root设备，但不应阻止logcat
                ["adb", "logcat", "-c"]
            ]
            for cmd_args in init_commands:
                if self.stop_event.is_set():
                    logger.info("Logcat启动在初始化阶段被中断")
                    initialization_successful = False
                    break
                
                logger.info(f"执行初始化命令: {' '.join(cmd_args)}")
                init_proc = None
                try:
                    # 对于Windows，如果adb不在PATH中，可能需要指定完整路径
                    # shell=True有时用于解决路径问题，但Popen列表参数更好
                    init_proc = subprocess.Popen(
                        cmd_args,
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        creationflags=subprocess.CREATE_NO_WINDOW # Windows: 不创建控制台窗口
                    )
                    self.process_list.append(init_proc)
                    
                    # 使用communicate获取输出并等待，带超时
                    try:
                        stdout_data, stderr_data = init_proc.communicate(timeout=10) # 增加超时
                    except subprocess.TimeoutExpired:
                        logger.error(f"命令 {' '.join(cmd_args)} 超时 (10s)")
                        init_proc.kill() # 强制杀死超时的进程
                        stdout_data, stderr_data = init_proc.communicate() # 再次communicate获取残留输出
                        initialization_successful = False
                        if cmd_args[1] == "wait-for-device": break # 关键步骤失败
                        continue # 其他命令超时可以尝试继续

                    if init_proc.returncode != 0:
                        log_func = logger.warning
                        # wait-for-device 失败是致命的
                        if cmd_args[1] == "wait-for-device":
                            log_func = logger.error
                            initialization_successful = False
                        
                        log_func(f"命令 {' '.join(cmd_args)} 返回码: {init_proc.returncode}")
                        if stdout_data: log_func(f"  stdout: {stdout_data.decode(errors='replace').strip()}")
                        if stderr_data: log_func(f"  stderr: {stderr_data.decode(errors='replace').strip()}")
                        if not initialization_successful: break
                    else:
                        logger.info(f"命令 {' '.join(cmd_args)} 执行成功")

                except FileNotFoundError:
                    logger.error(f"命令 'adb' 未找到。请确保ADB已安装并配置在系统PATH中。")
                    initialization_successful = False
                    break
                except Exception as e:
                    logger.error(f"执行 {' '.join(cmd_args)} 时出错: {e}", exc_info=True)
                    initialization_successful = False
                    if cmd_args[1] == "wait-for-device": break
                finally: # 确保在每次迭代后，如果init_proc已创建，其管道被关闭
                    if init_proc:
                        if init_proc.stdout and not init_proc.stdout.closed: init_proc.stdout.close()
                        if init_proc.stderr and not init_proc.stderr.closed: init_proc.stderr.close()
            
            if not initialization_successful:
                logger.error("Logcat 初始化失败，主 logcat 进程不会启动。")
                return # finally 块会执行清理

            logger.info("Logcat 初始化成功，启动主 logcat 进程。")
            p_obj = subprocess.Popen(
                ["adb", "logcat", "-v", "time"], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, # 捕获stderr以诊断adb logcat本身的问题
                creationflags=subprocess.CREATE_NO_WINDOW # Windows: 不创建控制台窗口
            )
            self.process_list.append(p_obj)
            
            # 启动读取线程
            self._log_reader_thread = threading.Thread(
                target=self._threaded_log_reader,
                args=(p_obj.stdout, self._log_queue, self.stop_event),
                name="LogcatReaderThread"
            )
            self._log_reader_thread.daemon = True # 设置为守护线程，主程序退出时它也会退出
            self._log_reader_thread.start()

            last_activity_time = time.time()
            max_inactivity_no_output = 30 # 秒

            while not self.stop_event.is_set():
                try:
                    if self._log_queue is None:
                        logger.warning("队列已被清理，退出主循环")
                        break
                    if self.is_running is False:
                        logger.info("Logcat已停止运行，退出主循环")
                        break
                    # 从队列中获取日志行，设置超时以便能定期检查stop_event
                    line_bytes = self._log_queue.get(timeout=0.1) # 100ms超时
                    
                    if line_bytes is None: # 收到读取线程的结束信号
                        logger.info("主循环：读取线程已结束，logcat流终止。")
                        break 

                    # 成功读取到日志
                    log = line_bytes.decode("utf-8", errors='replace').strip()
                    last_activity_time = time.time() 
                    
                    if log:
                        if grep in log:
                            adb_connect_device.add_vds_app_msgs(log + "\n")
                        adb_connect_device.add_os_msgs(log + "\n")
                        from case.CaseManager import case_manager
                        if not case_manager.abnormal_stop:
                            for mark in self.error_marks:
                                if self.check_error_and_emit(log, mark):
                                    break
                    
                except queue.Empty: # 队列在超时时间内为空，正常情况
                    # 检查logcat进程是否意外退出
                    if p_obj.poll() is not None:
                        logger.warning(f"adb logcat进程在主循环中检测到意外终止。返回码: {p_obj.returncode}")
                        stderr_output = ""
                        if p_obj.stderr: # 尝试读取stderr
                            try: stderr_output = p_obj.stderr.read().decode(errors='replace')
                            except: pass
                        if stderr_output: logger.warning(f"adb logcat stderr: {stderr_output.strip()}")
                        break # 退出主循环

                    # 检查超时
                    if time.time() - last_activity_time > max_inactivity_no_output:
                        logger.warning(f"Logcat在 {max_inactivity_no_output} 秒内无任何输出，可能设备断开或logcat卡住。")
                        # 这里不 break，因为 stop_event 是外部控制的。
                        # 但可以考虑发送一个信号或记录更严重的警告。
                        # 如果需要因此停止，则应该 self.stop_event.set()
                        # 或者，如果logcat卡住，尝试重启adb server可能是一种策略，但这更复杂
                        # 为了简单起见，我们现在只记录警告并依赖外部停止
                        # 为了避免无限循环的警告，更新last_activity_time
                        last_activity_time = time.time() 
                    continue # 继续循环，检查stop_event和队列
                
                except Exception as e: # 捕获其他可能的异常，例如解码错误
                    logger.error(f"处理logcat队列输出时发生异常: {e}", exc_info=True)
                    # 决定是否中断，取决于错误类型
                    # break # 发生未知错误，中断循环

            # 循环结束 (因为 stop_event.is_set() 或 break)
            logger.info("Logcat主处理循环已退出。")

            # 确保读取线程被通知停止 (如果它还没有因为EOF或其他错误停止)
            # 虽然stop_event应该已经被设置，但这里多一步确保
            if not self.stop_event.is_set():
                 self.stop_event.set() # 确保读取线程知道要退出

        except FileNotFoundError:
            logger.error(f"主logcat命令 'adb' 未找到。请确保ADB已安装并配置在系统PATH中。")
            initialization_successful = False # 虽然晚了，但标记一下
        except Exception as e:
            logger.error(f"Logcat启动或主循环中发生未捕获的严重异常: {e}", exc_info=True)
        finally:
            logger.info("进入Logcat的finally清理块...")

            # 等待读取线程结束
            if self._log_reader_thread and self._log_reader_thread.is_alive():
                logger.info("等待Logcat读取线程结束...")
                # 不需要再次set stop_event，因为它在主循环退出条件中已经处理
                # 如果读取线程因为readline()阻塞，关闭p_obj.stdout可以帮助它解除阻塞
                if p_obj and p_obj.stdout and not p_obj.stdout.closed:
                    try:
                        p_obj.stdout.close() # 这应该会使读取线程的readline()返回EOF或错误
                    except Exception as e_close_stdout:
                        logger.debug(f"关闭p_obj.stdout时出错: {e_close_stdout}")
                
                self._log_reader_thread.join(timeout=5.0) # 给读取线程一些时间退出
                if self._log_reader_thread.is_alive():
                    logger.warning("Logcat读取线程在超时后仍未结束。")
            
            self.cleanup_processes() # 清理所有我们创建的进程 (包括 p_obj)

            with self.start_stop_lock:
                self.is_running = False
                self._log_reader_thread = None # 清理线程引用
                self._log_queue = None
            logger.info("Logcat已停止并清理完毕。")
            if self.stop_event and not self.stop_event.is_set() and initialization_successful:
                # 如果初始化成功，但循环不是因为stop_event退出的
                logger.warning("Logcat似乎意外停止。")


mcu_log_reader: LogReader = LogReader()
soc_log_reader: LogReader = LogReader()
logcat_reader: LogReader = LogReader()

if __name__ == '__main__':
    mcu_log_reader.open("COM6", 115200)
    threading.Thread(target=mcu_log_reader.start_logcat).start()
