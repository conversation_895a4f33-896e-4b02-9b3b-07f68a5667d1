from collections import OrderedDict
from queue import Queue

import binascii
import datetime
import json
import logging
import operator
import random
import re
import socket
import subprocess
import threading
import os
import openpyxl
from openpyxl.styles import Font, Alignment

import time
import traceback

import isotp
from can import Message, detect_available_configs
from udsoncan.connections import PythonIsoTpConnection
from adb.util.calcTools import high_low_to_int  # 不要删

from common.LogUtils import logger
from utils.DtcDetectManager import dtc_detect_manager
from utils.Influxdb import influx_client

from utils.SignalsManager import signals_manager
from photics import photics_manager
from adb import CANBusDevice
from adb.canoe.vector import CanoeBus
from adb.lin.PLinView import PLinApiView
from adb.pcan.PcanBus import PcanBus
from adb.zlgcan.zlgBase import ZCAN_LIB

ADB_FORWARD_TIMEOUT = 10


class AdbConnectDevice(object):

    def __init__(self):
        self.is_send_msg = True
        self.step_dict = {}
        self.is_pass_dict = {}
        self.socket_client = None
        self.can_bus = None
        self.canoe_bus = None
        self.pcan_bus = None
        self.zlg_bus = None
        self.lin_bus = None
        self.tsmaster_bus = None
        self.tsmaster_lin_bus = None
        self.cycle_can_msgs = []
        self.cycle_can_contents = []
        self.lin_msg_count = 0
        self.cycle_lin_msgs = []
        self.can_msgs = []
        self.mcu_msgs = []
        self.soc_msgs = []
        self.os_msgs = []
        self.vds_app_msgs = []
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)
        signals_manager.vds_status_signal.connect(self.update_vds_status)
        signals_manager.process_monitor_status.connect(self.update_process_monitor_status)
        self.detect_adb_forward_timer = None
        self.adb_forward_heartbeat_receive_time = int(time.time())
        self.adb_forward_status = False
        self.adb_forward_port1 = 8000
        self.adb_forward_port2 = 30000
        self.adb_forward_interrupt = False
        self.can_bus_list = []
        self.can_msg_delay = 1

        self.check_recv_msg_flag = False
        self.check_recv_msg_queue = Queue()
        self.check_recv_msg_except = []
        self.detect_click = False
        self.test_result = None
        self.start_aeq_time = ''
        self.current_time = ''
        self.adb_lock = threading.Lock()

        # 随机亮度控制变量
        self.random_brightness_timer = None
        self.random_brightness_running = False
        self.random_brightness_mode = ""
        self.random_brightness_start = 0
        self.random_brightness_end = 0
        self.random_brightness_end_time = 0
        self.process_monitor_alert_time = 0
        self.process_monitor_config = {}
        threading.Thread(target=self.load_process_monitor_config, ).start()
        # self.enable_process_monitor = False

    def load_process_monitor_config(self):
        path = os.path.join(os.getcwd(), "configs", "process_monitor.json")
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.loads(f.read())
                self.process_monitor_config = data['config']
                # self.process_monitor_config = json.loads(f.read())['config']
        except Exception as e:
            logger.error(f"load_process_monitor_config error: {e}")
            self.process_monitor_config = {}

    def check_recv_can_msg(self, ):
        while self.check_recv_msg_flag:
            recv_msg, success = self.can_bus._recv_internal(timeout=3)
            # if recv_msg:
            #     logging.info(f"check_recv_can_msg recv_msg={recv_msg}")
            messages = recv_msg if isinstance(recv_msg, list) else [recv_msg]
            for index, msg in enumerate(messages):
                if self.check_recv_msg_except:
                    recv_id = self.check_recv_msg_except[0]
                    expect_msg = self.check_recv_msg_except[1]
                else:
                    recv_id = "0"
                    expect_msg = "FF FF FF FF FF FF FF FF"

                dtc_detect_manager.handle_dtc_error(msg)

                if msg and msg.arbitration_id == int(recv_id, 16):
                    logger.info(f"check_recv_can_msg recv_msg={msg}")
                    converted = ' '.join(format(x, '02X') for x in msg.data)
                    if converted[0:24].strip() == expect_msg.strip():
                        self.detect_click = True
                        continue
                    else:
                        self.detect_click = False
                        break

    def record_can_recv_msg(self, timeout=3):
        start = time.time()

        while time.time() - start < timeout:
            recv_msg, success = self.can_bus._recv_internal(timeout=3)
            self.add_can_msgs(recv_msg, "recv")

    def is_adb_forward_connected(self):
        return self.adb_forward_status

    def reset_can_msg_delay(self, delay):
        self.can_msg_delay = delay

    def check_adb_forward_heartbeat(self):
        self.detect_adb_forward_timer = threading.Timer(1, self.check_adb_forward_heartbeat)
        self.detect_adb_forward_timer.start()

        interval = int(time.time()) - self.adb_forward_heartbeat_receive_time
        logger.debug('check_adb_forward_heartbeat interval={}'.format(interval))
        if interval > ADB_FORWARD_TIMEOUT:
            self.stop_adb_forward_heartbeat()
            self.adb_forward_status = False
            signals_manager.update_adb_forward_status.emit(self.adb_forward_status)
            logger.info('check_adb_forward_heartbeat adb forward heartbeat timeout')
            if not self.adb_forward_interrupt and photics_manager.vds_status:
                threading.Timer(interval=3, function=self.reconnect_adb_forward).start()

    def stop_adb_forward_heartbeat(self):
        logger.debug("stop_adb_forward_heartbeat")
        if self.detect_adb_forward_timer is not None:
            self.detect_adb_forward_timer.cancel()
            self.detect_adb_forward_timer = None

    def reconnect_adb_forward(self):
        logger.info("reconnect_adb_forward")
        self.adb_forward_heartbeat_receive_time = int(time.time())
        self.stop_adb_forward_heartbeat()
        self.disconnect_adb_forward()
        self.connect_adb_forward(port1=self.adb_forward_port1, port2=self.adb_forward_port2)

    def update_adb_forward_str_msg(self, action, value):
        if operator.eq("HeartBreak", action):
            self.adb_forward_heartbeat_receive_time = int(time.time())
            if not self.adb_forward_status:
                self.adb_forward_status = True
                threading.Thread(target=self.set_vds_system_time).start()
                signals_manager.update_adb_forward_status.emit(self.adb_forward_status)
        else:
            logger.debug("update_adb_forward_str_msg action={}, value={}".format(action, value))

    def update_vds_status(self, status):
        logger.info("update_vds_status status=%s", status)
        if status:
            self.reconnect_adb_forward()

    def connect_device(self, device, baudrate, can_type):
        try:
            logger.info(f"connect_device device={device}, baudrate={baudrate}, can_type={can_type}")
            self.can_bus_list.clear()
            if device == 0:
                devices = detect_available_configs("vector")
                logging.info(f"connect_device canoe devices={devices}")
                for device in devices:
                    channel = device['channel']
                    app_name = device['app_name']
                    self.can_bus = CanoeBus(channel=channel, baudrate=baudrate, can_type=can_type,
                                            app_name=app_name)
                    self.can_bus.name = "canoe"
                    if self.can_bus.state.name == "ACTIVE":
                        self.can_bus.status = True
                    else:
                        self.can_bus.status = False
                    self.canoe_bus = self.can_bus
                    self.can_bus_list.append(self.can_bus)
            elif device == 1:
                self.can_bus = PcanBus(baudrate, can_type)
                self.can_bus.name = "pcan"
                self.pcan_bus = self.can_bus
                self.can_bus_list.append(self.can_bus)
                self.can_bus_list.append(self.can_bus)
            elif device == 2:
                self.can_bus = ZCAN_LIB(str(baudrate), can_type)
                self.can_bus.name = "zlg"
                self.zlg_bus = self.can_bus
                self.can_bus_list.append(self.can_bus)
                self.can_bus_list.append(self.can_bus)
            elif device == 3:
                self.can_bus = PLinApiView(str(baudrate))
                self.can_bus.name = "lin"
                self.lin_bus = self.can_bus
                self.can_bus_list.append(self.can_bus)
                self.can_bus_list.append(self.can_bus)
            elif device == 4:
                from adb.TSMaster.TSmasterBase import TSMaster
                for i in range(2):
                    self.can_bus = TSMaster(can_type, i, baudrate)
                    self.can_bus.name = "tsmaster"
                    self.can_bus_list.append(self.can_bus)
                self.tsmaster_bus = self.can_bus
            elif device == 5:
                from adb.TSMaster.LinComm import LinCommunicator
                self.can_bus = LinCommunicator(baudrate=19.2, channel=0)
                self.can_bus.name = "tsmaster-lin"
                self.can_bus.status = self.can_bus.connect() == 0
                self.can_bus_list.append(self.can_bus)
                self.tsmaster_lin_bus = self.can_bus

            if self.can_bus is not None and self.can_bus.status:
                signals_manager.can_device_connect.emit(device, True)
                return True
            else:
                signals_manager.can_device_connect.emit(device, False)
                return False
        except Exception as e:
            print(traceback.format_exc())
            logger.error("connect_device exception: {}".format(str(e.args)))

    def get_canbus_device(self, bus_type=CANBusDevice.PCAN):
        if bus_type == CANBusDevice.CANOE:
            return self.canoe_bus
        elif bus_type == CANBusDevice.PCAN:
            return self.pcan_bus
        elif bus_type == CANBusDevice.ZLG:
            return self.zlg_bus
        elif bus_type == CANBusDevice.LIN:
            return self.lin_bus
        elif bus_type == CANBusDevice.TSMaster:
            return self.tsmaster_bus
        else:
            return None

    def zlg_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        try:
            logger.info(f"zlg_send_and_read_msg case_number={case_number}, command={command}, msg={msg}, "
                        f"can_dict={can_dict}")
            if self.can_bus is not None:
                send_id = can_dict["id"]
                if int(send_id, 16) != 0:
                    self.can_bus.send(can_dict)
                    self.add_can_msgs(can_dict)

            if not operator.eq("0", str(can_dict["cycle_period"])):
                # 循环周期性时间不为0则开启CAN消息循环发送，并且将CAN消息ID注册到循环发送CAN消息的ID集合中
                if msg.frame.can_id not in self.cycle_can_msgs:
                    # 防止开启多个CAN消息发送循环
                    self.cycle_can_msgs.append(msg.frame.can_id)
                    self.cycle_can_contents.append(can_dict["msg"])
                    self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, can_dict, can_dict)
                if msg.frame.can_id in self.cycle_can_msgs and can_dict["msg"] not in self.cycle_can_contents:
                    index = self.cycle_can_msgs.index(msg.frame.can_id)
                    self.cycle_can_contents[index] = can_dict["msg"]
                    self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, can_dict, can_dict)

            if can_dict["expect"][-1].strip() == "00 00 00 00 00 00 00 00":
                start_time = time.time()
                while time.time() - start_time < self.can_msg_delay:
                    recv_msgs = self.can_bus._recv_internal(timeout)  # 释放消息
                    self.add_can_msgs(recv_msgs, "recv")
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                return

            if can_dict["expect"][-1].strip() == "FF FF FF FF FF FF FF FF":
                # 不需要验证回复报文 报文发送成功判定PASS
                negative_feedback = True
            else:
                negative_feedback = False
            start_time = time.time()
            expected_id = can_dict["recv_id"]
            # expected_data = can_dict["expect"][-1].upper().strip()
            flag = "PASS" if negative_feedback else "NG"  # 修改flag的初始值根据negative_feedback
            result = ""
            verify = False
            verify_data = []
            if '(' and ')' and '|' in can_dict["expect"][-1]:
                verify = True
                verify_data = can_dict["expect"][-1].split('|')[-1].strip()
                # expected_data = can_dict["expect"][-1].split('|')[0].strip()
            if not verify:
                matches_found = OrderedDict((exp.upper().strip(), False) for exp in can_dict["expect"])

            else:
                matches_found = OrderedDict((exp, False) for exp in can_dict["expect"])
            while time.time() - start_time < timeout:
                recv_msgs, c = self.can_bus._recv_internal(timeout)
                self.add_can_msgs(recv_msgs, "recv")
                if not recv_msgs:
                    continue
                for recv_msg in recv_msgs:
                    if recv_msg and recv_msg.arbitration_id == int(expected_id, 16):
                        logger.info(f"zlg_send_and_read_msg : recv_msg ={recv_msg} ")
                        if negative_feedback:  # 消极匹配
                            flag = "NG"  # 如果是negative_feedback，收到expected_id的报文则立即失败
                            break  # 立即跳出循环
                        else:  # 正向匹配
                            converted = ' '.join(format(x, '02X') for x in recv_msg.data)
                            logger.info(f"recv id :{expected_id}   real:{converted} ")

                            for expected_data in can_dict["expect"]:
                                if not verify:
                                    expected_data = expected_data.upper().strip()
                                if not matches_found[expected_data]:
                                    expected_data = expected_data.replace('\\\\', "\\")
                                    match = re.match(r"{}".format(expected_data), converted, re.M | re.I)
                                    if match:
                                        if verify:
                                            data_verify = re.findall(r"{}".format(expected_data), converted,
                                                                     re.M | re.I)
                                            logging.info(f"data_verify:{data_verify}", )
                                            if len(data_verify) == 0:
                                                flag = "NG"
                                                result += converted
                                                continue
                                                # return flag
                                            elif isinstance(data_verify[0], tuple):
                                                data_verify = data_verify[0]

                                            logging.info(
                                                f"zlg_send_and_read_msg result = {data_verify}, re rule: {expected_data}, "
                                                f"eval({verify_data})")
                                            try:
                                                result_match = eval(verify_data)  # 使用eval()安全地计算表达式
                                                logger.info(
                                                    f"zlg_send_and_read_msg verify_data = {verify_data}, result = {result}")
                                                if result_match:
                                                    flag = "PASS"
                                                    matches_found[expected_data] = True
                                                    result += converted
                                                else:
                                                    flag = "NG"
                                            except Exception as e:
                                                print(str(e.args))
                                                logger.info(f"canoe_send_and_read_msg data_verify = {verify_data}")
                                                logger.info(traceback.format_exc())
                                                flag = "NG"
                                                result += converted

                                        else:
                                            matches_found[expected_data] = True
                                            result += converted
                                            logger.info(f"Matched expectation: {expected_data}")
                                            break  # 找到一个匹配就处理下一个报文
                                    else:
                                        # if converted not in result:
                                        result += converted

                            # 检查是否所有预期数据都已匹配
                            if all(matches_found.values()):
                                flag = "PASS"
                                break
                if flag == "PASS":
                    break
            # if not negative_feedback:
            #     flag = "PASS" if "".join(can_dict["expect"]).upper().strip() in result.upper().strip() else "NG"
            signals_manager.step_execute_finish.emit(case_number, command, flag, result[-64 * 3:])
        except Exception as e:
            # print(str(e.args))
            logger.error("zlg_send_and_read_msg exception: {}".format(traceback.format_exc()))

    def canoe_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        logging.info(f"canoe_send_and_read_msg case_number={case_number}, command={command}, msg={msg}, "
                     f"can_dict={can_dict}")
        if self.can_bus is not None:
            send_id = can_dict["id"]
            if int(send_id, 16) != 0:
                self.can_bus.send(msg)
                self.add_can_msgs(can_dict)

        if not operator.eq("0", str(can_dict["cycle_period"])):
            # 循环周期性时间不为0则开启CAN消息循环发送，并且将CAN消息ID注册到循环发送CAN消息的ID集合中
            if msg.arbitration_id not in self.cycle_can_msgs:
                # 防止开启多个CAN消息发送循环
                self.cycle_can_msgs.append(msg.arbitration_id)
                self.cycle_can_contents.append(can_dict["msg"])
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)
            if msg.arbitration_id in self.cycle_can_msgs and can_dict["msg"] not in self.cycle_can_contents:
                index = self.cycle_can_msgs.index(msg.arbitration_id)
                self.cycle_can_contents[index] = can_dict["msg"]
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)

        if can_dict["expect"][-1].strip() == "00 00 00 00 00 00 00 00":
            start_time = time.time()
            while time.time() - start_time < self.can_msg_delay:
                recv_msgs, success = self.can_bus._recv_internal(timeout)  # 释放消息
                self.add_can_msgs(recv_msgs, "recv")
            # 不需要验证回复报文 报文发送成功判定PASS
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            return
        if can_dict["expect"][-1].strip() == "FF FF FF FF FF FF FF FF":
            # 不需要验证回复报文 报文发送成功判定PASS
            negative_feedback = True
        else:
            negative_feedback = False
        start_time = time.time()
        expected_id = can_dict["recv_id"]
        # expected_data = can_dict["expect"][-1].upper().strip()
        flag = "PASS" if negative_feedback else "NG"  # 修改flag的初始值根据negative_feedback
        result = ""
        verify = False
        verify_data = []
        if '(' and ')' and '|' in can_dict["expect"][-1]:
            verify = True
            verify_data = can_dict["expect"][-1].split('|')[-1].strip()
            expected_data = can_dict["expect"][-1].split('|')[0].strip()
        if not verify:
            matches_found = OrderedDict((exp.upper().strip(), False) for exp in can_dict["expect"])

        else:
            matches_found = OrderedDict((exp, False) for exp in can_dict["expect"])
        while time.time() - start_time < timeout + 1:
            recv_msg, success = self.can_bus._recv_internal()
            # logging.info(f"canoe_send_and_read_msg recv_msg={recv_msg}")
            if recv_msg is not None:
                self.add_can_msgs(recv_msg, "recv")
            if recv_msg and recv_msg.arbitration_id == int(expected_id, 16):
                logging.info(f"canoe_send_and_read_msg recv_msg={recv_msg}")
                if negative_feedback:
                    flag = "NG"  # 如果是negative_feedback，收到expected_id的报文则立即失败
                    break  # 立即跳出循环
                else:
                    converted = ' '.join(format(x, '02X') for x in recv_msg.data[:recv_msg.dlc])
                    # print("dlc:", recv_msg.dlc, "data:", converted)
                    result += converted

                    for expected_data in can_dict["expect"]:
                        if not verify:
                            expected_data = expected_data.upper().strip()
                        if not matches_found[expected_data]:
                            expected_data = expected_data.replace('\\\\', "\\")
                            print("match expected_data:", expected_data, converted)
                            match = re.match(r"{}".format(expected_data), converted, re.M | re.I)
                            if match:
                                if verify:
                                    data_verify = re.findall(r"{}".format(expected_data), converted, re.M | re.I)

                                    if len(data_verify) == 0:
                                        flag = "NG"
                                        result += converted
                                        continue
                                    elif isinstance(data_verify[0], tuple):
                                        data_verify = data_verify[0]

                                    logging.info(
                                        f"canoe_send_and_read_msg result = {data_verify}, re rule: {expected_data}, "
                                        f"eval({verify_data})")
                                    try:
                                        result_match = eval(verify_data)  # 使用eval()安全地计算表达式
                                        logger.info(
                                            f"canoe_send_and_read_msg verify_data = {verify_data}, result = {result}")
                                        if result_match:
                                            flag = "PASS"
                                            matches_found[expected_data] = True
                                            result += converted
                                        else:
                                            flag = "NG"
                                    except Exception as e:
                                        print(str(e.args))
                                        logger.info(f"canoe_send_and_read_msg data_verify = {verify_data}")
                                        logger.info(traceback.format_exc())
                                        flag = "NG"
                                        result += converted

                                else:
                                    matches_found[expected_data] = True
                                    flag = "PASS"
                                    logger.info(f"Matched expectation: {expected_data}")

                            else:
                                result += converted
                                continue
                    # 检查是否所有预期数据都已匹配
                    print("matches_found values", matches_found.values())
                    if all(matches_found.values()):
                        flag = "PASS"
                        break
        # if not negative_feedback:
        #     flag = "PASS" if "".join(can_dict["expect"]).upper().strip() in result.upper().strip() else "NG"

        signals_manager.step_execute_finish.emit(case_number, command, str(flag), result[-64 * 3:])

    def pcan_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        logger.info(f"pcan_send_and_read_msg case_number={case_number}, command={command}, msg={msg}, "
                    f"can_dict={can_dict}")
        if self.can_bus is not None:
            send_id = can_dict["id"]
            if int(send_id, 16) != 0:
                self.can_bus.send(msg)
                self.add_can_msgs(can_dict)

        if not operator.eq("0", str(can_dict["cycle_period"])):
            # 循环周期性时间不为0则开启CAN消息循环发送，并且将CAN消息ID注册到循环发送CAN消息的ID集合中
            if msg.arbitration_id not in self.cycle_can_msgs:
                # 防止开启多个CAN消息发送循环
                self.cycle_can_msgs.append(msg.arbitration_id)
                self.cycle_can_contents.append(can_dict["msg"])
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)
            if msg.arbitration_id in self.cycle_can_msgs and can_dict["msg"] not in self.cycle_can_contents:
                index = self.cycle_can_msgs.index(msg.arbitration_id)
                self.cycle_can_contents[index] = can_dict["msg"]
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)

        if can_dict["expect"][-1].strip() == "00 00 00 00 00 00 00 00":
            start_time = time.time()
            while time.time() - start_time < self.can_msg_delay:
                recv_msg = self.can_bus._recv_internal(timeout)  # 释放消息
                self.add_can_msgs(recv_msg, "recv")
            # 不需要验证回复报文 报文发送成功判定PASS
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            return
        if can_dict["expect"][-1].strip() == "FF FF FF FF FF FF FF FF":
            # 不需要验证回复报文 报文发送成功判定PASS
            negative_feedback = True
        else:
            negative_feedback = False

        start_time = time.time()
        expected_id = can_dict["recv_id"]
        # expected_data = can_dict["expect"][-1].upper().strip()
        flag = "PASS" if negative_feedback else "NG"  # 修改flag的初始值根据negative_feedback
        result = ""

        verify = False
        verify_data = []
        if '(' and ')' and '|' in can_dict["expect"][-1]:
            verify = True
            verify_data = can_dict["expect"][-1].split('|')[-1].strip()
            # expected_data = can_dict["expect"][-1].split('|')[0].strip()
        if not verify:
            matches_found = OrderedDict((exp.upper().strip(), False) for exp in can_dict["expect"])

        else:
            matches_found = OrderedDict((exp, False) for exp in can_dict["expect"])
        while time.time() - start_time < timeout:
            recv_msg, success = self.can_bus._recv_internal(timeout)
            # print(f"pcan_send_and_read_msg recv_msg={recv_msg}, success={success}")
            self.add_can_msgs(recv_msg, "recv")
            if recv_msg and recv_msg.arbitration_id == int(expected_id, 16):
                logger.info(f"pcan_send_and_read_msg : recv_msg ={recv_msg} {success}")
                if negative_feedback:
                    flag = "NG"  # 如果是negative_feedback，收到expected_id的报文则立即失败
                    break  # 立即跳出循环
                else:
                    converted = " ".join(format(x, '02X') for x in recv_msg.data)
                    result += converted
                    for expected_data in can_dict["expect"]:
                        if not verify:
                            expected_data = expected_data.upper().strip()
                        if not matches_found[expected_data]:
                            expected_data = expected_data.replace('\\\\', "\\")
                        match = re.match(r"{}".format(expected_data), converted, re.M | re.I)

                        if match:
                            if verify:
                                data_verify = re.findall(r"{}".format(expected_data), converted, re.M | re.I)

                                if len(data_verify) == 0:
                                    flag = "NG"
                                    result += converted
                                    continue
                                if isinstance(data_verify[0], tuple):
                                    data_verify = data_verify[0]

                                logging.info(
                                    f"pcan_send_and_read_msg result = {data_verify}, re rule: {expected_data}, "
                                    f"eval({verify_data})")
                                try:
                                    result_match = eval(verify_data)  # 使用eval()安全地计算表达式
                                    logger.info(
                                        f"pcan_send_and_read_msg verify_data = {verify_data}, result = {result}")
                                    if result_match:
                                        flag = "PASS"
                                        matches_found[expected_data] = True
                                        result += converted
                                    else:
                                        flag = "NG"
                                except Exception as e:
                                    # print(str(e.args))
                                    logger.info(f"pcan_send_and_read_msg data_verify = {verify_data}")
                                    logger.info(traceback.format_exc())
                                    flag = "NG"
                                    result += converted
                            else:
                                matches_found[expected_data] = True
                                result += converted
                                # flag = "PASS"
                                logger.info(f"Matched expectation: {expected_data}")

                        else:
                            result += converted
                            continue

            # 检查是否所有预期数据都已匹配
            if all(matches_found.values()):
                flag = "PASS"
                break
        # if not negative_feedback:
        #     flag = "PASS" if "".join(can_dict["expect"]).upper().strip() in result.upper().strip() else "NG"
        signals_manager.step_execute_finish.emit(case_number, command, str(flag), result[-64 * 3:])

    def tsmaster_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        self.canoe_send_and_read_msg(case_number, command, msg, can_dict, timeout=timeout)

    def can_send_and_read_msg(self, msg, register_id, timeout=3):
        logger.info(f"can_send_and_read_msg msg={msg}, register_id={register_id}")
        if self.can_bus is not None:
            self.can_bus.send(msg)

        ret = None
        start_time = time.time()
        while time.time() - start_time < timeout:
            recv_msg, success = self.can_bus._recv_internal(timeout)
            if recv_msg is not None and recv_msg.arbitration_id == int(register_id, 16):
                logger.info(f"can_send_and_read_msg recv_msg={recv_msg}, success={success}")
                ret = " ".join(format(x, '02X') for x in recv_msg.data)
                break

        return ret

    def add_can_msgs(self, msg, msg_type="send"):
        if isinstance(msg, tuple):
            return
        if msg is None:
            return
        try:

            # 获取当前时间，精确到毫秒
            now = datetime.datetime.now()
            # 格式化时间
            t = now.strftime("%Y-%m-%d %H:%M:%S.") + f"{now.microsecond // 1000:03d}"
            if isinstance(msg, Message):
                data = " ".join(format(x, '02X') for x in msg.data)
                msg = f"{t} {msg_type}:{hex(msg.arbitration_id)} {data}"
                self.can_msgs.append(msg + "\n")
            elif isinstance(msg, list):
                for m in msg:
                    if m is None:
                        continue
                    if isinstance(m, Message):
                        data = " ".join(format(x, '02X') for x in m.data)
                        m = f"{t} {msg_type}:{hex(m.arbitration_id)} {data}"
                        self.can_msgs.append(m + "\n")
            elif isinstance(msg, dict):
                data = msg["msg"]
                msg = f"{t} {msg_type}:{msg['id']} {data}"
                self.can_msgs.append(msg + "\n")
        except Exception as e:
            print(str(e.args))
            print(traceback.format_exc())

    def add_mcu_msgs(self, msg):
        self.mcu_msgs.append(msg)

    def add_soc_msgs(self, msg):
        self.soc_msgs.append(msg)

    def add_os_msgs(self, msg):
        self.os_msgs.append(msg)

    def add_vds_app_msgs(self, msg):
        self.vds_app_msgs.append(msg)

    def start_cycle_can_msg(self, interval, msg, can_dict):
        if isinstance(msg, Message):
            can_id = msg.arbitration_id
        else:
            can_id = int(msg["id"], 16)
        if can_id not in self.cycle_can_msgs or can_dict["msg"] not in self.cycle_can_contents:
            # print("self.cycle_can_msgs:",self.cycle_can_msgs)
            # print("self.cycle_can_contents:",self.cycle_can_contents)
            return

        threading.Timer(interval, self.start_cycle_can_msg, args=(interval, msg, can_dict)).start()
        if self.can_bus is not None:
            self.can_bus.send(msg)
            self.add_can_msgs(msg)

    @staticmethod
    def crc_checksum(ptr):
        crc = 0x00
        for iptr in ptr:
            crc ^= int(iptr, 16)
            for index in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x1d
                else:
                    crc = (crc << 1)

        str_crc = '0x{:02x}'.format(crc & 0xff)
        ptr.insert(0, str_crc[2:])
        return " ".join(ptr)

    def plin_send_msg(self, step):
        ptr = step.get("msg").split(" ")[1:]
        count = self.lin_msg_count % 15
        self.lin_msg_count += 1
        value = hex(int(ptr.pop(0), 16) >> 4)[2:] + hex(count)[2:]
        ptr.insert(0, value)
        msg = self.crc_checksum(ptr)
        msg_id = step.get("id")
        status = self.can_bus.write_message(
            msg_id,
            msg,
            step.get("direction"),
            step.get("check_number_type"),
            len(step.get("msg").split())
        )
        logger.info("execute_lin status={}".format(status))
        if step.get("period") == "True":
            interval = float(step.get("period_time")) * 0.001
            if step.get("id") not in self.cycle_lin_msgs:
                self.cycle_lin_msgs.append(msg_id)
                self.start_cycle_lin_msg(interval, step)

    def start_cycle_lin_msg(self, interval, step):
        lin_msg_id = step.get("id")
        if lin_msg_id not in self.cycle_lin_msgs:
            return

        threading.Timer(interval, self.start_cycle_lin_msg, args=(interval, step)).start()
        if self.can_bus is not None:
            self.plin_send_msg(step)

    @staticmethod
    def set_vds_system_time(retry_time=5):
        logger.info("set_vds_system_time")
        current_time = datetime.datetime.now().strftime("%m%d%H%M%Y.%S")
        cmd = f"adb root && adb shell date {current_time}"
        for i in range(retry_time):
            try:
                out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT, stdin=subprocess.PIPE, bufsize=0,
                                       stdout=subprocess.PIPE, encoding="utf8")
                output, error = out.communicate()
                logger.info(f"set_vds_system_time output={output}, error={error}")
                if "adbd is already running as root" in output:
                    break
                else:
                    time.sleep(2)
                    continue
            except Exception as e:
                logger.error(f"set_vds_system_time exception: {str(e.args)}")

    def stop_cycle_can_msg(self, msg_id):
        logger.info("stop_cycle_can_msg msg_id={}, cycle_can_msgs={}".format(msg_id, self.cycle_can_msgs))
        if msg_id in self.cycle_can_msgs:
            # 判段msg_id的索引
            index = self.cycle_can_msgs.index(msg_id)
            # 删除msg_id
            self.cycle_can_msgs.pop(index)
            # 删除self.cycle_can_contents索引的那个值
            self.cycle_can_contents.pop(index)

    def clear_cycle_can_msg(self):
        logger.info("clear_cycle_can_msg")
        self.cycle_can_msgs.clear()
        self.cycle_lin_msgs.clear()
        self.cycle_can_contents.clear()

    @staticmethod
    def adb_forward(port1=9000, port2=30000):
        """
        执行端口转发
        :return:
        """
        try:
            cmd = f'adb forward tcp:{port1} tcp:{port2}'
            out = subprocess.Popen(cmd, shell=False, stderr=subprocess.STDOUT, stdin=subprocess.PIPE, bufsize=0,
                                   stdout=subprocess.PIPE, encoding="utf8")
            output, error = out.communicate()
            logger.info("adb_forward output={} ,error={}".format(output, error))
        except Exception as e:
            logger.error("adb_forward exception: {}".format(str(e.args)))

    def connect_adb_forward(self, interrupt=False, port1=8000, port2=30000):
        logger.info(f"connect_adb_forward port1={port1}, port2={port2}")
        self.adb_forward_heartbeat_receive_time = int(time.time())
        self.adb_forward_interrupt = interrupt
        self.adb_forward_port1 = port1
        self.adb_forward_port2 = port2
        try:
            self.adb_forward(port1=self.adb_forward_port1, port2=self.adb_forward_port2)
            if self.socket_client is None:
                self.socket_client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket_client.connect(("127.0.0.1", self.adb_forward_port1))
            logger.info("connect_adb_forward socket_client={}".format(self.socket_client))
            threading.Thread(target=self.read_tcp_socket_data).start()
        except Exception as e:
            logger.error("connect_adb_forward exception: {}".format(str(e.args)))
        self.stop_adb_forward_heartbeat()
        self.check_adb_forward_heartbeat()
        return False

    def start_adb_forward_durability_test(self):
        threading.Timer(interval=1, function=self.start_adb_forward_durability_test).start()
        index = random.randint(0, 27)
        self.switch_pattern(index)

    def disconnect_adb_forward(self, interrupt=False):
        logger.info(f"disconnect_adb_forward interrupt={interrupt}")
        self.stop_adb_forward_heartbeat()
        self.adb_forward_interrupt = interrupt
        self.adb_forward_status = False
        if self.socket_client is not None:
            self.socket_client.close()
            self.socket_client = None
        return True

    def adb_forward_send_data(self, action, data_type="str", data=""):
        logger.info(f"adb_forward_send_data action={action}, data_type={data_type}, data={data}")
        try:
            content = {"action": action, "param": None, "data": data, "isLoop": False, "cmd": [], "value": None}
            logger.info(f"adb_forward_send_data content={content}")
            if self.socket_client is not None:
                self.socket_client.send((json.dumps(content)+"\n").encode())
        except Exception as e:
            logger.error("adb_forward_send_data exception: {}".format(e.args))

    def read_light_sensor(self):
        self.adb_forward_send_data(action="readLightSensor")

    def write_brightness_coefficient(self, coefficient):
        self.adb_forward_send_data(action="writeBrightnessCoefficient", data=coefficient)

    def read_screen_temp(self):
        self.adb_forward_send_data(action="readScreenTemp")

    def read_pcb_temp(self):
        self.adb_forward_send_data(action="readPCBTemp")

    def switch_color(self, color):
        self.adb_forward_send_data(action="switchColor", data=color)

    def fast_switch_color(self, start, color1, color2):
        self.adb_forward_send_data(action="fastSwitchColor", data=f"{start},{color1},{color2}")

    def switch_pattern(self, pattern):
        self.adb_forward_send_data(action="switchPattern", data=pattern)

    def display_table_point(self, rows, cols, interval, bg_color):
        """
        显示表格点阵
        :param rows: 表格行数
        :param cols: 表格列数
        :param interval: 单元格切换间隔时间(s)
        :param bg_color: 单元格切换的目标背景色
        """
        data = f"{rows},{cols},{interval},{bg_color}"
        self.adb_forward_send_data(action="displayTablePoint", data=data)

    def read_software_version(self):
        self.adb_forward_send_data(action="readSoftwareVersion")

    def write_hardware_version(self, version):
        self.adb_forward_send_data(action="writeHardwareVersion", data=version)

    def read_hardware_version(self, length="0"):
        self.adb_forward_send_data(action="readHardwareVersion", data=length)

    def write_part_number(self, version):
        self.adb_forward_send_data(action="writePartNumber", data=version)

    def read_part_number(self):
        self.adb_forward_send_data(action="readPartNumber")

    def read_ld_version(self):
        self.adb_forward_send_data(action="readLDVersion")

    def read_tddi_version(self):
        self.adb_forward_send_data(action="readTDDIVersion")

    def read_tcon_version(self):
        self.adb_forward_send_data(action="readTCONVersion")

    def read_boot_version(self):
        self.adb_forward_send_data(action="readBootVersion")

    def read_tp_version(self):
        self.adb_forward_send_data(action="readTpVersion")

    def read_assembly_version(self):
        self.adb_forward_send_data(action="readAssemblyVersion")

    def write_hwsn(self, sn):
        self.adb_forward_send_data(action="writeHWSN", data=sn)

    def read_hwsn(self, length="0"):
        self.adb_forward_send_data(action="readHWSN", data=length)

    def write_psn(self, sn):
        self.adb_forward_send_data(action="writePSN", data=sn)

    def read_psn(self, length="0"):
        self.adb_forward_send_data(action="readPSN", data=length)

    def read_inner_software_version(self):
        self.adb_forward_send_data(action="readInnerSoftwareVersion")

    def read_inner_hardware_version(self, length="0"):
        self.adb_forward_send_data(action="readInnerHardwareVersion", data=length)

    def switch_brightness(self, brightness):
        self.adb_forward_send_data(action="switchBrightness", data=brightness)

    def execute_random_brightness(self, case_number, command, mode, start_value, end_value, duration, interval = 0.2):
        """
        在指定时间内随机切换亮度值
        
        Args:
            case_number: 测试用例编号
            command: 执行的命令
            mode: 亮度模式
            start_value: 起始亮度值
            end_value: 终止亮度值
            duration: 持续时间(秒)
            interval: 切换间隔时间(秒)
        """
        try:
            # 停止之前可能正在运行的任务
            if self.random_brightness_timer is not None:
                self.random_brightness_timer.cancel()
                self.random_brightness_timer = None

            # 设置初始亮度
            brightness = f"{mode}:{start_value}"
            self.switch_brightness(brightness)

            # 设置控制变量
            self.random_brightness_running = True
            self.random_brightness_mode = mode
            self.random_brightness_start = start_value
            self.random_brightness_end = end_value
            self.random_brightness_end_time = time.time() + duration

            # 启动定时器
            logger.info(f"随机亮度变化开始: mode={mode}, start={start_value}, end={end_value}, duration={duration}s")
            self.random_brightness_timer = threading.Timer(interval, self._brightness_timer_callback, args=(interval,))
            self.random_brightness_timer.start()

            # 不阻塞主线程，立即返回
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "随机亮度变化已开始")

        except Exception as e:
            logger.error(f"execute_random_brightness exception: {str(traceback.format_exc())}")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"执行异常: {str(e)}")

    def _brightness_timer_callback(self, interval = 0.2):
        """内部方法：Timer回调函数，用于随机切换亮度"""
        try:
            # 检查是否应该继续运行
            from utils.PowerManager import power_manager
            from case.CaseManager import case_manager, CaseStatus
            if case_manager.status == CaseStatus.FINISH:
                logger.info("_brightness_timer_callback canceled")
                # 清理运行状态
                self.random_brightness_running = False
                self.random_brightness_timer = None
                return

            if not self.random_brightness_running:
                return

            current_time = time.time()

            # 检查是否已超过总持续时间
            if current_time >= self.random_brightness_end_time:
                # A. 设置最终亮度并结束
                final_brightness = f"{self.random_brightness_mode}:{self.random_brightness_end}"
                self.switch_brightness(final_brightness)

                # 清理运行状态
                self.random_brightness_running = False
                self.random_brightness_timer = None
                return

            # B. 继续执行随机亮度变化
            # 生成随机亮度值
            random_value = random.randint(
                min(self.random_brightness_start, self.random_brightness_end),
                max(self.random_brightness_start, self.random_brightness_end)
            )

            # 构建并发送亮度命令
            brightness = f"{self.random_brightness_mode}:{random_value}"
            self.switch_brightness(brightness)

            # 继续设置定时器
            self.random_brightness_timer = threading.Timer(interval, self._brightness_timer_callback, args=(interval,))
            self.random_brightness_timer.start()

        except Exception as e:
            logger.error(f"_brightness_timer_callback exception: {str(traceback.format_exc())}")
            self.random_brightness_running = False
            self.random_brightness_timer = None

    def read_brightness(self):
        self.adb_forward_send_data(action="readBrightness")

    def read_aeq_task(self, times=1, interval_Up=7, interval_Down=3, volt=0, channel=1):
        """
        执行AEQ任务相关的ADB命令
        1. 禁用AEQ任务
        2. 读取寄存器值，按照指定次数和间隔重复读取
        3. 将读取结果保存到Excel
        
        Args:
            times: 读取次数
            interval: 读取间隔(秒)
            
        Returns:
            str: 格式为 'success:excel文件路径:最后一次读取值' 或 'fail:错误信息'
        """
        from utils.PowerManager import power_manager
        try:
            # 创建Excel文件
            item = {
                "test_function": "readAeqTask",
                "header": ["序号", "时间", "读取值"],
                "content": [],
                "volt": volt,
                "channel": channel
            }
            # 获取当前时间
            self.current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_path = os.path.join(os.getcwd(), f"AEQ_Task_Read_{self.current_time}.xlsx")

            # 创建Excel工作簿和工作表
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "AEQ任务读取结果"

            # 设置表头
            headers = ["序号", "时间", "读取值"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

                # 调整列宽
            ws.column_dimensions['A'].width = 10  # 序号
            ws.column_dimensions['B'].width = 25  # 时间
            ws.column_dimensions['C'].width = 15  # 读取值

            last_result = None

            # 循环读取指定次数
            with self.adb_lock:
                for i in range(times):
                    if self.start_aeq_time == '':
                        self.start_aeq_time = self.current_time
                    else:
                        if self.start_aeq_time != self.current_time:
                            self.start_aeq_time = self.current_time
                            logger.info(f"{self.current_time}:用户主动停止用例")
                            return f'fail:用户主动停止用例'
                    # 第一个命令：禁用AEQ任务
                    power_status, set_status = power_manager.set_volt(volt, channel)
                    if not power_status:
                        logger.error("设置电压失败")
                    if not set_status:
                        logger.error("设置电压失败")
                    time.sleep(interval_Up)
                    cmd1 = "adb shell \"echo -1 > /sys/devices/platform/16330000.i2c/i2c-3/3-0006/enable\""
                    out1 = subprocess.Popen(cmd1, shell=True, stderr=subprocess.STDOUT, stdin=subprocess.PIPE,
                                            stdout=subprocess.PIPE, encoding="utf8")
                    output1, error1 = out1.communicate()
                    logger.info(f"read_aeq_task [{i + 1}/{times}] cmd1 output={output1}, error={error1}")
                    time.sleep(0.5)

                    # 第二个命令：读取寄存器值
                    cmd2 = "adb shell \"i2cget -f -y 3 0x2c 0x3b\""
                    out2 = subprocess.Popen(cmd2, shell=True, stderr=subprocess.STDOUT, stdin=subprocess.PIPE,
                                            stdout=subprocess.PIPE, encoding="utf8")
                    output2, error2 = out2.communicate()
                    logger.info(f"read_aeq_task [{i + 1}/{times}] cmd2 output={output2}, error={error2}")
                    # 判断adb是否连接上
                    if output2.startswith("adb"):
                        logger.error("adb连接失败")
                        return f'fail:adb连接失败'

                    # 处理读取结果
                    result = output2.strip() if output2 else "None"
                    last_result = result

                    # 记录当前时间
                    test_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

                    # 写入Excel
                    row = i + 2  # 从第2行开始写数据
                    ws.cell(row=row, column=1, value=i + 1)  # 序号
                    ws.cell(row=row, column=2, value=test_time)  # 时间
                    ws.cell(row=row, column=3, value=result)  # 读取值

                    # 每10次保存一次Excel文件
                    if (i + 1) % 10 == 0 or i == times - 1:
                        try:
                            wb.save(excel_path)
                            logger.info(f"已保存前{i + 1}次读取结果到Excel: {excel_path}")
                        except Exception as e:
                            logger.error(f"保存Excel文件失败: {str(e.args)}")

                    item["content"].append([i + 1, test_time, result])

                    # 如果不是最后一次读取，则等待指定的间隔时间
                    if i < times - 1:
                        time.sleep(0.5)
                        power_status, set_status = power_manager.set_volt(0, channel)
                        if not power_status:
                            logger.error("设置电压失败")
                        if not set_status:
                            logger.error("设置电压失败")
                        time.sleep(interval_Down)

            # 最后保存一次Excel文件
            try:
                wb.save(excel_path)
                logger.info(f"所有读取结果已保存到Excel文件: {excel_path}")
            except Exception as e:
                logger.error(f"保存Excel文件失败: {str(e.args)}")

            # 返回成功状态、Excel文件路径和最后一次读取值
            self.test_result = item
            return f'success:{excel_path}:{last_result}'
        except Exception as e:
            logger.error(f"read_aeq_task exception: {str(traceback.format_exc())}")
            return f'fail:{str(e.args)}'

    def switch_back_light(self, status):
        self.adb_forward_send_data(action="switchBackLight", data=status)

    def read_back_light_status(self):
        self.adb_forward_send_data(action="readBackLightStatus")

    def switch_bist_pattern(self, status):
        self.adb_forward_send_data(action="switchBistPattern", data=status)

    def read_bist_pattern_status(self):
        self.adb_forward_send_data(action="readBistPatternStatus")

    def display_reboot(self):
        self.adb_forward_send_data(action="displayReboot")

    def tcon_reset(self):
        self.adb_forward_send_data(action="tconReset")

    def switch_sleep(self):
        self.adb_forward_send_data(action="switchSleep")

    def switch_wakeup(self):
        self.adb_forward_send_data(action="switchWakeup")

    def switch_dp(self, command):
        # command: open/close
        self.adb_forward_send_data(action="switchDP", data=command)

    def test_tpcm(self):
        # 测试TPCM值
        self.adb_forward_send_data(action="testTPCM")

    def test_touch_still(self):
        self.adb_forward_send_data(action="testTouchStill")

    def test_touch_marking(self, marking_mode):
        self.adb_forward_send_data(action="testTouchMarking", data=marking_mode)

    def test_touch_resp_times(self):
        self.adb_forward_send_data(action="testTouchRespTimes")

    def test_touch_resp_times_start(self):
        self.adb_forward_send_data(action="testTouchRespCountStart")

    def test_touch_resp_times_end(self):
        self.adb_forward_send_data(action="testTouchRespCountEnd")

    def test_touch_resp_time(self):
        self.adb_forward_send_data(action="testTouchRespTime")

    def test_touch_report_rate(self):
        self.adb_forward_send_data(action="testTouchReportRate")

    def i2c_checksum_detect(self, i2c_data, checksum):
        data = f"{i2c_data}:{checksum}"
        self.adb_forward_send_data(action="i2cChecksumDetect", data=data)

    def forward_xyz_data(self, data):
        self.adb_forward_send_data(action="forwardXyzData", data=data)

    def forward_xyz_finish(self):
        self.adb_forward_send_data(action="forwardXyzFinish")

    def read_color_temp(self):
        self.adb_forward_send_data(action="readColorTemp")

    def notify_brightness_calibrate_start(self):
        self.adb_forward_send_data(action="notifyBrightnessCalibrateStart")

    def notify_brightness_calibrate_finish(self):
        self.adb_forward_send_data(action="notifyBrightnessCalibrateFinish")

    def send_serial_protocol_data(self, data):
        self.adb_forward_send_data(action="serialProtocolTest", data=data)

    def read_tcp_socket_data(self):
        """
        读取TCP网口数据
        :return:
        """
        try:
            buffer = ''
            while self.is_connect():
                socket_bytes = self.socket_client.recv(1024)
                # print("socket_bytes", socket_bytes)
                if len(socket_bytes) > 0:
                    data = socket_bytes.decode()
                    buffer += data
                    while '\n' in buffer:
                        message, buffer = buffer.split('\n', 1)
                        logger.debug("read_tcp_socket_data message={}".format(message))
                        if message.strip():
                            self.dispatch_message(message)
                time.sleep(0.02)
        except Exception as e:
            print(traceback.format_exc())
            logger.error("read_tcp_socket_data exception: {}".format(str(e.args)))

    def update_process_monitor_status(self, recv_list):
        from fs_manager.FSManager import fs_manager
        from utils.ProjectManager import project_manager
        project_number = project_manager.get_test_plan_project_number()
        project_name = project_manager.get_test_plan_project_name()
        test_plan_name = project_manager.get_test_plan_name()
        test_plan_id = project_manager.get_test_plan_id()
        uuid = project_manager.get_test_record_id()
        from case.CaseManager import case_manager, CaseStatus

        if case_manager.status == CaseStatus.FINISH:
            return
        try:
            psn = recv_list[0]["psn"].replace(" ", "")
        except Exception:
            psn = "null"
        cur_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        print("project_number:", project_number)
        # 接口数据上传
        ERROR_LIST = self.process_monitor_config.get(project_number, {}).get("ERROR_LIST", [])
        print("ERROR_LIST:", ERROR_LIST)
        print("recv_list:", recv_list)
        for item in recv_list:
            index = recv_list.index(item)
            child_failt = ""
            tmp = {
                "psn": psn,
                "extra_info": "",
                "project_number": project_number,
                "run_id": uuid,
                "project_name": project_name
            }
            if "value" in item.keys():
                value = item["value"]
                code = item["name"]
                try:
                    # from utils.ProjectManager import project_manager
                    from case.StepManager import step_manager
                    step = step_manager.get_current_step()
                    case_number = step.get("case_number", "")
                    case_name = step.get("case_name", "")
                    project_number = project_manager.get_test_plan_project_number()
                    project_name = project_manager.get_test_plan_project_name()
                    test_plan_name = project_manager.get_test_plan_name()
                    test_plan_id = project_manager.get_test_plan_id()
                    machine_number = project_manager.get_machine_number()
                except:
                    continue

                # 更新界面显示
                # for i, value in enumerate(current_values):
                try:
                    influx_client.write_data_multi(
                        table=str(project_number),
                        tags={
                            "project_name": project_name,
                            "test_plan_name": test_plan_name,
                            "test_plan_id": test_plan_id,
                            "machine_number": machine_number,
                            "case_number": case_number,
                            "case_name": case_name,
                            # "channel": f"chl{i + 1}"
                        },
                        fields={code: float(value)}
                    )
                except Exception as e:
                    logger.warning(f"update_work_current2influxdb error: {e}")  # 记录错误日志，以便调试和排除问题
                    break

                continue
            tmp["code"] = item["name"]
            tmp["status"] = item["status"]
            tmp["test_plan_name"] = test_plan_name
            tmp["test_plan_id"] = test_plan_id
            # 防止 list index out of range 错误
            if index < len(ERROR_LIST):
                tmp["name"] = ERROR_LIST[index]
            else:
                # 如果索引超出范围，使用默认值或item的name
                tmp["name"] = item.get("name", f"Unknown_Error_{index}")
                logger.warning(f"ERROR_LIST index {index} out of range (length: {len(ERROR_LIST)}), using fallback name")
            tmp["value"] = str(item["status"])
            tmp["cur_time"] = cur_time

            # 如果数据为空，则跳过
            if not item:
                continue
            if "HeartBreak" in item:
                continue
            # psn
            if item["status"] == 1:
                if "faultList" in item.keys():
                    child_failt = [i["faultName"] + " error!" for i in item["faultList"]]
                    child_failt = "".join(child_failt)
                else:
                    # 没有子故障信息
                    child_failt = item["name"] + " error!"
            elif item["status"] == 0:
                pass
            else:
                tmp["value"] = str(item["status"])
            tmp["extra_info"] = child_failt
            if tmp["status"] == 1:
                threading.Thread(target=fs_manager.post_process_monitor_exp_submit, args=(tmp,)).start()
                if time.time() - self.process_monitor_alert_time > 60:
                    self.process_monitor_alert_time = time.time()
                    data = {
                        "code": tmp["code"],
                        "name": tmp['name'],
                        "cur_time": tmp['cur_time'],
                        "project_number": tmp['project_number'],
                        "test_plan_name": test_plan_name,
                        # "test_plan_id": test_plan_id,
                        "app_name": "ATEApp",
                        "station_name": project_manager.get_machine_number(),
                    }
                    threading.Thread(target=fs_manager.post_process_monitor_send_exp_msg, args=(data,)).start()

    @staticmethod
    def dispatch_message(message):
        data = json.loads(message)
        if isinstance(data, list):
            if len(data) == 1:
                data = data[0]
            if len(data) > 1:
                # 过程监控故障上报
                if signals_manager.enable_process_monitor:
                    logger.info(f"dispatch_message process_monitor_status: {data}")
                    signals_manager.process_monitor_status.emit(data)
        if data.__contains__("HeartBreak"):
            heart_break = data["HeartBreak"]
            logger.debug("dispatch_message action={}, value={}".format("HeartBreak", heart_break))
            signals_manager.reply_adb_forward_str_msg.emit("HeartBreak", heart_break)
        elif data.__contains__("action"):
            action = data["action"]
            value = data.get("param", "")
            logger.info("dispatch_message action={}, value={}".format(action, value))
            signals_manager.reply_adb_forward_str_msg.emit(action, value)

    def is_connect(self):
        return self.socket_client is not None and not getattr(self.socket_client, '_closed')

    def is_can_bus_open(self):
        return self.can_bus is not None and self.can_bus.status

    def close_device(self):
        logger.info("close_device")
        try:
            if self.can_bus.name == "canoe":
                self.can_bus.shutdown()
                self.can_bus = None
            elif self.can_bus.name == "pcan":
                self.can_bus.close_pcan_channel()
                self.can_bus = None
            elif self.can_bus.name == "zlg":
                self.can_bus.close_channel_device()
                self.can_bus = None
            elif self.can_bus.name == "lin":
                self.can_bus.disconnect()
                self.can_bus = None
            elif self.can_bus.name == "tsmaster":
                self.can_bus.shutdown()
                self.can_bus = None
            elif self.can_bus.name == "tsmaster-lin":
                self.can_bus.disconnect()
                self.can_bus = None
                self.tsmaster_lin_bus = None
            return True
        except Exception as e:
            print(traceback.format_exc())
            logger.error("close_device exception: {}".format(str(e.args)))
            return False

    def open_uds(self, tx_id=int("636", 16), rx_id=int("6b6", 16)):
        """can总线相关配置"""
        if isinstance(tx_id, str) or isinstance(rx_id, str):
            tx_id = eval(tx_id)
            rx_id = eval(rx_id)
        iso_tp_params = {
            'stmin': 32,
            # Will request the sender to wait 32ms between consecutive frame. 0-127ms or 100-900ns with values from 0xF1-0xF9
            'blocksize': 0,
            # Request the sender to send 8 consecutives frames before sending a new flow control message
            'wftmax': 0,  # Number of wait frame allowed before triggering an error
            'tx_data_length': 8,  # Link layer (CAN layer) works with 8 byte payload (CAN 2.0)
            'tx_data_min_length': None,
            # Minimum length of CAN messages. When different from None, messages are padded to meet this length. Works with CAN 2.0 and CAN FD.
            'tx_padding': 0,  # Will pad all transmitted CAN messages with byte 0x00.
            'rx_flowcontrol_timeout': 1000,
            # Triggers a timeout if a flow control is awaited for more than 1000 milliseconds
            'rx_consecutive_frame_timeout': 1000,
            # Triggers a timeout if a consecutive frame is awaited for more than 1000 milliseconds
            'squash_stmin_requirement': False,
            # When sending, respect the stmin requirement of the receiver. If set to True, go as fast as possible.
            'max_frame_size': 4095  # Limit the size of receive frame.
        }

        try:
            tp_addr = isotp.Address(isotp.AddressingMode.Normal_11bits, txid=tx_id, rxid=rx_id)  # 网络层寻址方法
            tp_stack = isotp.CanStack(bus=self.can_bus, address=tp_addr, params=iso_tp_params)  # 网络/传输层（IsoTP 协议）
            self.uds_conn = PythonIsoTpConnection(tp_stack)  # 应用层和传输层之间建立连接
            self.uds_conn.mtu = 4098
            self.uds_conn.open()
            print(f'open uds {hex(tx_id)}, {hex(rx_id)}')
        except Exception as e:
            print(traceback.format_exc())
            print(e.args)
            return False
        else:
            return True

    def close_uds(self):
        if self.uds_conn.is_open():
            self.uds_conn.close()

    def uds_request_respond(self, command):
        """发送uds请求和接收uds响应"""
        if self.uds_conn is None or not self.uds_conn.is_open():
            return False, 'uds is not open'
        if not isinstance(command, str):  # 判断command数据类型
            command = str(int(command))
        request_pdu = binascii.a2b_hex(command.replace(' ', ''))  # 处理command
        # 耗时统计
        try:
            # rs = self.uds_conn.specific_send(request_pdu)  # 发送uds请求
            rs = self.uds_conn.send(request_pdu)  # 发送uds请求
            # payload = self.uds_conn.wait_frame(timeout=3)
            # print("payload:", payload)

        except Exception as e:
            print("发送请求失败", str(e.args))
        else:
            print('UDS发送请求：%s' % request_pdu)
        try:
            resp_pdu = self.uds_conn.specific_wait_frame(timeout=1)  # 接收uds响应
        except Exception as e:
            print(traceback.format_exc())
            print('响应数据失败', str(e.args))
            return False, str(e.args)
        else:
            res = resp_pdu.hex().upper()
            respond = ''
            for i in range(len(res)):
                if i % 2 == 0:
                    respond += res[i]
                else:
                    respond += res[i] + ' '
            print('UDS响应结果：%s' % respond)
            return True, respond.strip()

    def start_process_monitor(self, ):
        # content = {'action': 'start', 'param': 1}
        # self.socket_client.send(json.dumps(content).encode())
        signals_manager.enable_process_monitor = True

    def stop_process_monitor(self, ):
        # content = {'action': 'start', 'param': 1}
        # self.socket_client.send(json.dumps(content).encode())
        signals_manager.enable_process_monitor = False


adb_connect_device: AdbConnectDevice = AdbConnectDevice()
