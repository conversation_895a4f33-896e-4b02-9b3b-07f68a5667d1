import time
import threading

import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster

from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class ETMMU3Client:

    def __init__(self):
        self.device_name = None
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.slave_id = None
        self.timeout = None
        self._is_open = False
        self._master = None
        self._interrupt = False
        self._lock = threading.RLock()  # 添加线程锁

        # 动态适配多版本协议
        self.channel1_voltage_addr = 0x69
        self.channel1_current_addr = 0x6a
        self.channel2_voltage_addr = 0xcd
        self.channel2_current_addr = 0xce
        self.channel3_voltage_addr = 0x131
        self.channel3_current_addr = 0x132


    def set_voltage_current_addr(self, channel1_voltage_addr, channel1_current_addr, channel2_voltage_addr,
                                 channel2_current_addr, channel3_voltage_addr, channel3_current_addr):
        self.channel1_voltage_addr = channel1_voltage_addr
        self.channel1_current_addr = channel1_current_addr
        self.channel2_voltage_addr = channel2_voltage_addr
        self.channel2_current_addr = channel2_current_addr
        self.channel3_voltage_addr = channel3_voltage_addr
        self.channel3_current_addr = channel3_current_addr

    def set_interrupt(self, interrupt):
        self._interrupt = interrupt

    @property
    def is_open(self):
        return self._is_open

    def open(self, device_name, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, slave_id=1, timeout=5.0):
        with self._lock:  # 添加线程同步保护
            self.device_name = device_name
            self.port = port
            self.baudrate = baudrate
            self.bytesize = bytesize
            self.parity = parity
            self.stopbits = stopbits
            self.slave_id = slave_id
            self.timeout = timeout

            if not self._is_open:
                try:
                    if self._master:
                        self._master.close()
                    self._master = RtuMaster(
                        serial.Serial(port=self.port, baudrate=self.baudrate, bytesize=self.bytesize,
                                      parity=self.parity, stopbits=self.stopbits, timeout=2)
                    )
                    self._master.set_timeout(self.timeout)
                    self._master.open()
                    self._is_open = True
                    logger.info(f"open success: device={device_name}, port={port}")
                except Exception as e:
                    logger.error(f"open exception: {str(e.args)}")
                    self._is_open = False

            signals_manager.update_device_status_signal.emit(device_name, self._is_open)
            return self._is_open

    def close(self):
        with self._lock:  # 添加线程同步保护
            logger.info(f"close")
            if self._master:
                try:
                    self._master.close()
                except Exception as e:
                    logger.error(f"close exception: {str(e.args)}")

            self._master = None
            self._is_open = False

            return True

    def execute(self, function_code, starting_address, quantity_of_x=0, output_value=0,
                data_format="", expected_length=-1, write_starting_address_fc23=0):
        with self._lock:  # 添加线程同步保护
            if not self._master:
                return None
            logger.info(f"execute status={self._master._serial.is_open}, is_open={self._is_open}, "
                    f"starting_address={starting_address}")

            if not self._master._serial.is_open:
                return None

            if not self._is_open:
                return None

            try:
                r = self._master.execute(self.slave_id, function_code, starting_address, quantity_of_x, output_value,
                                         data_format, expected_length, write_starting_address_fc23)
                logger.info(f"execute r={r}")
                return r
            except Exception as e:
                logger.error(f"execute exception: {str(e.args)}")

            return None

    def reconnect(self):
        with self._lock:  # 添加线程同步保护
            # 尝试重新打开连接
            logger.info(f"reconnect interrupted={self._interrupt}, is_open={self._is_open}")
            if not self._interrupt and self.is_open:
                self.close()
                logger.info("reconnect attempt...")
                time.sleep(0.5)

                # 检查必要参数是否存在（修复参数检查逻辑）
                if (self.device_name and self.port and
                    self.baudrate is not None and self.bytesize is not None and
                    self.parity and self.stopbits is not None and
                    self.slave_id is not None and self.timeout is not None):

                    try:
                        self.open(self.device_name, self.port,
                                 self.baudrate or 115200, self.bytesize or 8,
                                 self.parity or "N", self.stopbits or 1,
                                 self.slave_id or 1, self.timeout or 5.0)
                    except Exception as e:
                        logger.error(f"reconnect open failed: {str(e.args)}")
                        return False
                else:
                    logger.error("reconnect failed: missing connection parameters")
                    return False

            return self._master is not None and self._master._serial.is_open

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, 1)
        if r is None:
            self._is_open = self.reconnect()
            logger.info(f"read_hr_one reconnect_status={self._is_open}")
            return None
        return r[0]

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
        return r

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        r = self.execute(defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
        if r is None:
            self._is_open = self.reconnect()
            logger.info(f"write_hr_one reconnect_status={self._is_open}")
        return r

    def write_hr_many(self, starting_address, data, data_format=""):
        """保持寄存器多个写入"""
        r = self.execute(defines.WRITE_MULTIPLE_REGISTERS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def switch(self, flag):
        if flag:
            self.write_hr_one(0, 1)
        else:
            self.write_hr_one(0, 0)

    def get_switch_status(self):
        return self.read_hr_one(1)

    def switch_ch1(self, flag):
        if flag:
            self.write_hr_one(0x64, 1)
        else:
            self.write_hr_one(0x64, 0)

    def switch_ch2(self, flag):
        if flag:
            self.write_hr_one(0xc8, 1)
        else:
            self.write_hr_one(0xc8, 0)

    def switch_ch3(self, flag):
        if flag:
            self.write_hr_one(0x12c, 1)
        else:
            self.write_hr_one(0x12c, 0)

    def set_volt_ch1(self, volt, protocol_type=0):
        if protocol_type == 0:
            volt = int(volt * 100)
            return self.write_hr_one(0x6d, volt)
        elif protocol_type == 1:
            volt = int(volt * 100)
            return self.write_hr_one(0x6e, volt)
        return None

    def get_volt_ch1(self):
        r = self.read_hr_one(0x6d)
        r = r / 100.0
        return r

    def set_curr_ch1(self, curr):
        curr = int(curr * 1000)
        return self.write_hr_one(0x6e, curr)

    def get_curr_ch1(self):
        r = self.read_hr_one(0x6e)
        r = r / 1000.0
        return r

    def get_volt_p_ch1(self):
        r = self.read_hr_one(self.channel1_voltage_addr)
        r = r / 100.0
        return r

    def get_curr_p_ch1(self):
        logger.debug(f"get_curr_p_ch1")
        r = self.read_hr_one(self.channel1_current_addr)
        if r is None:
            return None
        r = r / 1000.0
        return r

    def set_ovp_ch1(self, volt):
        volt = int(volt * 100)
        self.write_hr_one(0x6f, volt)

    def switch_ovp_ch1(self, flag):
        if flag:
            self.write_hr_one(0x70, 1)
        else:
            self.write_hr_one(0x70, 0)

    def set_ocp_ch1(self, curr):
        curr = int(curr * 1000)
        self.write_hr_one(0x71, curr)

    def switch_ocp_ch1(self, flag):
        if flag:
            self.write_hr_one(0x72, 1)
        else:
            self.write_hr_one(0x72, 0)

    def set_volt_ch2(self, volt, protocol_type=0):
        if protocol_type == 0:
            volt = int(volt * 100)
            return self.write_hr_one(0xd1, volt)
        elif protocol_type == 1:
            volt = int(volt * 100)
            return self.write_hr_one(0xd2, volt)
        return None

    def get_volt_ch2(self):
        r = self.read_hr_one(0xd1)
        r = r / 100.0
        return r

    def set_curr_ch2(self, curr):
        curr = int(curr * 1000)
        return self.write_hr_one(0xd2, curr)

    def get_curr_ch2(self):
        r = self.read_hr_one(0xd2)
        r = r / 1000.0
        return r

    def get_volt_p_ch2(self):
        r = self.read_hr_one(self.channel2_voltage_addr)
        r = r / 100.0
        return r

    def get_curr_p_ch2(self):
        r = self.read_hr_one(self.channel2_current_addr)
        if r is None:
            return None
        r = r / 1000.0
        return r

    def set_volt_ch3(self, volt, protocol_type=0):
        if protocol_type == 0:
            volt = int(volt * 100)
            return self.write_hr_one(0x135, volt)
        elif protocol_type == 1:
            volt = int(volt * 100)
            return self.write_hr_one(0x136, volt)
        return None

    def get_volt_ch3(self):
        r = self.read_hr_one(0x135)
        r = r / 100.0
        return r

    def set_curr_ch3(self, curr):
        curr = int(curr * 1000)
        return self.write_hr_one(0x136, curr)

    def get_curr_ch3(self):
        r = self.read_hr_one(0x136)
        r = r / 1000.0
        return r

    def get_volt_p_ch3(self):
        r = self.read_hr_one(self.channel3_voltage_addr)
        r = r / 100.0
        return r

    def get_curr_p_ch3(self):
        r = self.read_hr_one(self.channel3_current_addr)
        if r is None:
            return None
        r = r / 1000.0
        return r


client: ETMMU3Client = ETMMU3Client()
