import threading
import time

from common.LogUtils import logger
from . import power_manager
from .etm_mu3_client import client


class ETMMU3Control:

    def __init__(self):
        self.power_client = client
        self.protocol_type = 0

    def power_source_connect(self, device_name, port, interrupt=False):
        logger.info(f"power_source_connect device_name={device_name}, port={port}, interrupt={interrupt}")
        self.power_client.set_interrupt(interrupt)
        return self.power_client.open(device_name=device_name, port=port)

    def power_source_close(self, interrupt=True):
        logger.info("power_source_close")
        self.power_client.set_interrupt(interrupt)
        self.power_client.close()
        return True

    def power_on(self, channel: int = 1):
        if channel == 1:
            return self.power_client.switch_ch1(True)
        elif channel == 2:
            return self.power_client.switch_ch2(True)
        elif channel == 3:
            return self.power_client.switch_ch3(True)
        return None

    def power_off(self, channel: int = 1):
        if channel == 1:
            return self.power_client.switch_ch1(False)
        elif channel == 2:
            return self.power_client.switch_ch2(False)
        elif channel == 3:
            return self.power_client.switch_ch3(False)
        return None

    def is_open(self):
        return self.power_client.is_open

    def set_voltage(self, channel: int = 1, value: float = 0.0):
        """
        设置电压
        @param channel: 电源通道
        @param value: 电压值
        @return:
        """
        logger.info(f"set_voltage channel={channel}, value={value}")
        if channel == 1:
            r = self.power_client.set_volt_ch1(value, self.protocol_type)
            if r is None:
                return False, 'mu3_client写入通道1失败'
            return True, ""
        elif channel == 2:
            r = self.power_client.set_volt_ch2(value, self.protocol_type)
            if r is None:
                return False, 'mu3_client写入通道2失败'
            return True, ""
        elif channel == 3:
            r = self.power_client.set_volt_ch3(value, self.protocol_type)
            if r is None:
                return False, 'mu3_client写入通道3失败'
            return True, ""
        return False, 'mu3_client写入通道失败'

    def set_step_voltage(self, channel, start_volt, end_volt, interval, step):
        logger.info(f"set_step_voltage channel={channel}, start_volt={start_volt}, end_volt={end_volt}, "
                    f"interval={interval}, step={step}")
        status = True
        try:
            self.set_voltage(channel, start_volt)
            time.sleep(interval)

            if start_volt < end_volt:
                for i in range(int((end_volt - start_volt) / step) + 1):
                    if start_volt + i * step >= end_volt:
                        self.set_voltage(channel, end_volt)
                    else:
                        self.set_voltage(channel, start_volt + i * step)
                    time.sleep(interval)
            else:
                for i in range(int((start_volt - end_volt) / step) + 1):
                    if start_volt - i * step <= end_volt:
                        self.set_voltage(channel, end_volt)
                    else:
                        self.set_voltage(channel, start_volt - i * step)
                    time.sleep(interval)
        except Exception as e:
            logger.error(f"set_step_voltage exception: {str(e.args)}")
            status = False
        return status

    def set_voltage_current_addr(self, channel1_voltage_addr, channel1_current_addr, channel2_voltage_addr,
                                 channel2_current_addr, channel3_voltage_addr, channel3_current_addr):
        if self.power_client is not None:
            self.power_client.set_voltage_current_addr(channel1_voltage_addr, channel1_current_addr,
                                                       channel2_voltage_addr, channel2_current_addr,
                                                       channel3_voltage_addr, channel3_current_addr)

    def read_instant_voltage(self, channel):
        """
        读取瞬时电压
        @return:
        """
        instant_voltage = None
        if channel == 1:
            instant_voltage = self.power_client.get_volt_p_ch1()
        elif channel == 2:
            instant_voltage = self.power_client.get_curr_p_ch2()
        elif channel == 3:
            instant_voltage = self.power_client.get_curr_p_ch3()
        logger.debug(f"read_instant_voltage instant_voltage={instant_voltage}")
        return instant_voltage

    def read_instant_current(self, channel):
        """
        读取瞬时电流
        @return:
        """
        instant_current = None
        if channel == 1:
            instant_current = self.power_client.get_curr_p_ch1()
        elif channel == 2:
            instant_current = self.power_client.get_curr_p_ch2()
        elif channel == 3:
            instant_current = self.power_client.get_curr_p_ch3()
        logger.debug(f"read_instant_current instant_current={instant_current}")
        return instant_current

    def read_work_current(self, channel=1, times=3, interval=0.1):
        logger.debug(f"read_work_current channel={channel}, times={times}, interval={interval}")
        """
        读取电流
        @return:
        """
        status = True
        sum_work_current = 0
        for i in range(times):
            instant_current = self.read_instant_current(channel)
            if instant_current is None:
                status = False
                break
            sum_work_current += instant_current
            time.sleep(interval)
        average_work_current = round(sum_work_current / times, 3)
        if status:
            logger.info(f"read_work_current channel={channel}, average_work_current={average_work_current}")
        return status, average_work_current

    @staticmethod
    def start_power_on_time_detect(interval_time=30):
        threading.Timer(interval=interval_time, function=power_manager.set_power_on_ready, args=(True,)).start()

    def read_period_work_current(self, read_interval, read_time, min_current, max_current, channel):
        logger.info(f"read_period_work_current read_interval={read_interval}, read_time={read_time}, "
                    f"min_current={min_current}, max_current={max_current}, channel={channel}")
        period_status = True
        error_work_current = 0
        try:
            # 读取一次电流的总间隔时间为read_interval + 一次平均电流间隔0.15 *  5
            for i in range(int(read_time / (read_interval + 0.75))):
                status, work_current = self.read_work_current(channel=channel)
                if status:
                    # 电流读取成功
                    work_current = round(work_current, 3)
                    if min_current < work_current < max_current:
                        # 读取的电流在标定范围内，等待读取间隔时间后再次读取电流
                        time.sleep(read_interval)
                    else:
                        # 读取的电流不在标定范围内，返回结果False，如果关联耐久测试异常停止，则直接停止测试
                        period_status = False
                        error_work_current = work_current
                        break
                else:
                    logger.warning(f"read_period_work_current read work_current failed")
        except Exception as e:
            logger.error(f"read_period_work_current exception: {str(e.args)}")
            period_status = False
        return period_status, error_work_current


etm_mu3_control: ETMMU3Control = ETMMU3Control()
