import os

from common.LogUtils import logger
from nextcloud import NextCloud

NEXTCLOUD_USERNAME = 'automated_test_ftp1'
NEXTCLOUD_PASSWORD = 'Xu4rYMCw5E'
NEXTCLOUD_UID = "D6A25366-9A66-4BA4-8AB9-AB918E763DC7"
NEXTCLOUD_URL = 'https://ecmbeta.hiwaytech.com'
AUTOTEST_ROOT = 'automated_test'
to_js = True
HW_PLATFORM = 'http://10.1.1.132:8010/project/version/public/saveVersion'

cloud = NextCloud(endpoint=NEXTCLOUD_URL, user=NEXTCLOUD_USERNAME, password=NEXTCLOUD_PASSWORD, json_output=to_js)


def upload_folder_to_cloud(project, date, d_type, folder, local_folder):
    """
    上传文件到Nextcloud平台
    :param project:
    :param date:
    :param d_type:
    :param folder:
    :param local_folder:
    :return:
    """
    logger.info(f"upload_file_to_cloud project={project}, date={date}, d_type={d_type}, folder={folder}, "
                f"local_folder={local_folder}")
    status = True
    try:
        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}')

        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}')

        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}'):
            temp = d_type.split('/')
            if len(temp) == 1:
                cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}')
            else:
                cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{temp[0]}')
                cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{temp[0]}/{temp[1]}')

        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}')

        if cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}'):
            if local_folder is not None and os.path.exists(local_folder):
                ret = cloud.upload(NEXTCLOUD_UID, local_folder, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}')
                # logger.info(f'upload_folder {NEXTCLOUD_URL}/{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}')
                logger.info(f"upload_folder ret={ret}")
        else:
            status = False
    except Exception as e:
        status = False
        logger.info(f"upload_file_to_cloud exception: {str(e.args)}")

    return status


def upload_file_to_cloud(project_number, date, d_type, folder, file, local_file):
    """
    上传文件到Nextcloud平台
    :param project_number: 项目编号
    :param date: 日志日期
    :param d_type: 日志类型
    :param file:
    :param folder:
    :param local_file:
    :return:
    """
    logger.info(f"upload_file_to_cloud project={project_number}, date={date}, d_type={d_type}, folder={folder}, "
                f"file={file}, local_file={local_file}")
    status = True
    try:
        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}')

        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}')

        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{d_type}'):
            temp = d_type.split('/')
            if len(temp) == 1:
                cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{d_type}')
            else:
                cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{temp[0]}')
                cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{temp[0]}/{temp[1]}')

        if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{d_type}/{folder}'):
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{d_type}/{folder}')

        if cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project_number}/{date}/{d_type}/{folder}'):
            if local_file is not None and os.path.exists(local_file):
                ret = cloud.upload(NEXTCLOUD_UID, local_file,
                                   f'{AUTOTEST_ROOT}/{project_number}/{date}/{d_type}/{folder}/{file}')
                # logger.info(f'upload_file {NEXTCLOUD_URL}/{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}/{file}')
                # logger.info(f"upload_file ret={ret}")
        else:
            status = False
    except Exception as e:
        status = False
        logger.info(f"upload_file_to_cloud exception: {str(e.args)}")

    return status


def create_cloud_share(project, date, d_type, folder):
    """
    创建cloud平台的分享链接
    :param project:
    :param date:
    :param d_type:
    :param folder:
    :return:
    """
    if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}'):
        cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}')

    if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}'):
        cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}')

    if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}'):
        temp = d_type.split('/')
        if len(temp) == 1:
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}')
        else:
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{temp[0]}')
            cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{temp[0]}/{temp[1]}')

    if not cloud.check(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}'):
        cloud.mkdir(NEXTCLOUD_UID, f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}')
    response = cloud.create_share(f'{AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}', 3)
    # logger.info(f"create_cloud_share share={AUTOTEST_ROOT}/{project}/{date}/{d_type}/{folder}, response={response}")
    if response.is_ok:
        share_url = response.data['url']
        status = True
    else:
        # 生成分享链接失败之后重试
        # create_cloud_share(project, date, d_type, folder)
        # 默认是automated_test的目录
        share_url = 'https://ecmbeta.hiwaytech.com/'
        status = False
    # logger.info(f"create_cloud_share share_url={share_url}")
    return share_url, status
