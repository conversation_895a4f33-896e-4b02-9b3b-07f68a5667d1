import threading
from queue import Queue
from xml.etree import ElementTree

from PyQt5.QtCore import pyqtSignal, QObject
from PyQt5.QtGui import QCloseEvent, QPixmap
from can import Message
from photics.color_analyzer_tools.manager.BrightnessCurveCollect import BrightnessCurveCollect


class SignalsManager(QObject):
    reply_adb_forward_str_msg = pyqtSignal(str, str)
    optical_test_finished = pyqtSignal(list, list)
    contrast_test_finished = pyqtSignal(list, list)
    gamut_test_finished = pyqtSignal(list, list)
    brightness_test_finished = pyqtSignal(list, list)
    uniform_test_finished = pyqtSignal(list, list)
    white_balance_test_finished = pyqtSignal(list, list)
    temperature_test_finished = pyqtSignal(list, list)
    light_sensor_test_start = pyqtSignal()
    light_sensor_test_finished = pyqtSignal(list, list)
    reload_init_signal = pyqtSignal()
    update_step_status = pyqtSignal(str, str, str)
    update_step_start_time = pyqtSignal(str, str)
    update_step_end_time = pyqtSignal(str, str)
    update_case_status = pyqtSignal(int, str, str)
    update_case_result = pyqtSignal(str)
    update_case_pass_ng_times = pyqtSignal(str)
    draw_brightness_line = pyqtSignal(BrightnessCurveCollect)
    program_switch_power_on_off_process = pyqtSignal(str)
    program_switch_high_low_voltage_process = pyqtSignal(str)
    program_switch_random_voltage_process = pyqtSignal(str)
    load_project_config = pyqtSignal()
    save_project_config = pyqtSignal()
    app_close_signal = pyqtSignal(QCloseEvent)
    optical_change_page = pyqtSignal()
    temperature_change_page = pyqtSignal()
    light_sensor_change_page = pyqtSignal()
    available_camera_number = pyqtSignal(int)
    device_connect_state_signal = pyqtSignal(str, str)
    device_disconnect_state_signal = pyqtSignal(str, str)
    capture_image_signal = pyqtSignal(str)
    angle_result_signal = pyqtSignal(dict)
    adb_customize_cmd = pyqtSignal(str, str, str, str)
    i2c_cmd = pyqtSignal(dict, bool)
    start_light_sensor_curve_signal = pyqtSignal(str, str)
    start_env_temperature_rise_signal = pyqtSignal(str, str)
    step_execute_process = pyqtSignal(str, str, str)
    step_execute_finish = pyqtSignal(str, str, str, str)
    case_execute_finish = pyqtSignal(str, str, bool)
    rotate_xml_parse = pyqtSignal(ElementTree.Element)
    reload_xml = pyqtSignal()
    show_manual_decision_signal = pyqtSignal(str)
    power_switch_monitor_stopped = pyqtSignal(bool)
    test_module_signal = pyqtSignal(str)
    test_state_signal = pyqtSignal(str)
    detect_log_error = pyqtSignal(str)
    press_counter_signal = pyqtSignal(int)
    edit_dbc_signal = pyqtSignal(str, str, str)
    update_durability_case_info = pyqtSignal(str)
    update_test_status = pyqtSignal(object)
    update_project_info = pyqtSignal()
    update_project_extra_info = pyqtSignal(list)
    update_durability_case_result = pyqtSignal()
    update_adb_forward_status = pyqtSignal(bool)
    create_project_signal = pyqtSignal(str)
    show_update_case_signal = pyqtSignal(str)
    update_device_status_signal = pyqtSignal(str, bool)
    update_device_process_signal = pyqtSignal(str, str)
    update_case_module_signal = pyqtSignal(object, object)
    update_case_type_signal = pyqtSignal(object, object)
    canoe_recv_msg_queue = Queue()
    lock = threading.Lock()
    pull_plan = pyqtSignal()
    update_plan_detail = pyqtSignal(str, str, str, int, dict)
    update_v2_plan = pyqtSignal(list)
    update_v2_plan_detail = pyqtSignal(dict)
    machine_detail = pyqtSignal(str, str, str)
    export_logic_analyzer_data = pyqtSignal()
    gather_logic_analyzer_data = pyqtSignal(str)
    set_rotate_test_param = pyqtSignal(str, str)
    simulate_light_sensor = pyqtSignal(str)
    update_projects = pyqtSignal(list)
    clear_plan = pyqtSignal()
    init_steps = pyqtSignal()
    vision_calibrate_enable_status = pyqtSignal(bool)
    update_grainy_screen_detect_value = pyqtSignal(list)
    update_flicker_screen_detect_value = pyqtSignal(list)
    update_black_screen_detect_value = pyqtSignal(list)
    update_calibrate_frame = pyqtSignal(QPixmap)
    update_case_execute_times = pyqtSignal(int, int, int)
    can_device_connect = pyqtSignal(int, bool)
    current_canoe_asc_dir = ""
    video_test_finish = pyqtSignal(str, str)
    collect_video_changed = pyqtSignal(QPixmap)
    can_view_change = pyqtSignal(Message)
    vds_status_signal = pyqtSignal(bool)
    temp_data_update = pyqtSignal(list)
    operate_test_status = pyqtSignal(bool)
    flash_process = pyqtSignal(str)
    flash_log = pyqtSignal(str)
    canoe_update_process = pyqtSignal(str)
    tips_fs_err_msg = pyqtSignal(str, str)
    update_execute_bat_status = pyqtSignal(bool, str)
    process_monitor_status = pyqtSignal(list)
    enable_process_monitor = False


signals_manager: SignalsManager = SignalsManager()
