{
    "messages": [
        {
            "name": "VehicleSpeed", // = BO_ 291 VehicleSpeed: 8 Vector
            "id": 0x123, // 0x 前缀可选
            "ide": 0, // 0=11bit 标准帧 or 扩展帧
            "fd": 0, // 0=经典 CAN
            "dlc": 8,
            "cycle": 20,
            "signals": [
                {
                    "name": "Speed",
                    "start": 0,
                    "len": 16,
                    "little_endian": true,
                    "signed": false,
                    "factor": 0.01,
                    "offset": 0,
                    "min": 0,
                    "max": 655.35,
                    "unit": "km/h"
                }
            ]
        },
        {
            "name": "DiagResponse",
            "id": 0x18DAF110, // 扩展 CAN ID
            "ide": 1, // 1=29bit 扩展帧
            "fd": 1, // CAN-FD
            "brs": 1, // FD+BRS
            "dlc": 64, // 最长 64 字节
            "signals": [] // UDS 流式，直接发/收原始数据
        }
    ]
}